---
type: "always_apply"
---

-Documentation

You will use your Context7 MCP server along with web search to get updated documentation

Before debugging an issue, you will check the appropriate documentation to help understand/troubleshoot the issue

Before implementing a new feature, you will check the documentation


-Notes
There is a notes.md file
This file will contain extremely detailed notes on layout, code structure, API enpoints, known issues, file structure, previous debugging notes etc.  This will be referenced and updated regularly to make sure the project stays on track.  If you change something, note it.

-Checklist

there is a checklist.md file.  You will reference and update this as we move through the developement process.  This is a living checklist and should fully integrate with the notes.md file.  

-Github

Use github as appropriate.  It is an essential function.

Notes for Frontend

There is a notes_for_frontend.md file.  Ensure that this file contains all the needed information for the eventual build of the frontend.

-Other

Do not refer to the app as ready for deployment or productio ready unless it truly is.  Those statements are confusing.

Do not bypass functionality just to get a program working.  Fix the problem instead.

Keep your changes as small as possible.  Always consider what other files/functions your changes are going to affect.