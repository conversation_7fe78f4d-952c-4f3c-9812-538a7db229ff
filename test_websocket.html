<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message { padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px; }
        .user { background-color: #e3f2fd; }
        .agent { background-color: #f3e5f5; }
        .error { background-color: #ffebee; color: #c62828; }
        input, button { padding: 10px; margin: 5px; }
        input[type="text"] { width: 300px; }
        #messages { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Test with Proper UUIDs</h1>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div>
            <input type="text" id="messageInput" placeholder="Type your message here..." />
            <button onclick="sendMessage()">Send Message</button>
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>
        
        <div id="messages"></div>
    </div>

    <script>
        let ws = null;
        let sessionId = null;

        // Generate proper UUID v4
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function updateStatus(connected) {
            const statusEl = document.getElementById('status');
            if (connected) {
                statusEl.textContent = `Connected (Session: ${sessionId})`;
                statusEl.className = 'status connected';
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'status disconnected';
            }
        }

        function addMessage(type, content) {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${content}`;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('error', 'Already connected');
                return;
            }

            sessionId = generateUUID();
            const wsUrl = `ws://127.0.0.1:8000/ws/${sessionId}`;
            
            addMessage('info', `Connecting to ${wsUrl}`);
            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                updateStatus(true);
                addMessage('info', 'WebSocket connected successfully');
                
                // Send status command
                sendSystemCommand('status');
            };

            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    addMessage('agent', `${message.type}: ${JSON.stringify(message.data, null, 2)}`);
                } catch (error) {
                    addMessage('error', `Failed to parse message: ${event.data}`);
                }
            };

            ws.onclose = function() {
                updateStatus(false);
                addMessage('info', 'WebSocket disconnected');
            };

            ws.onerror = function(error) {
                addMessage('error', `WebSocket error: ${error}`);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendSystemCommand(command) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('error', 'Not connected');
                return;
            }

            const message = {
                type: 'system_command',
                data: {
                    command: command,
                    parameters: {}
                },
                timestamp: new Date().toISOString(),
                session_id: sessionId
            };

            ws.send(JSON.stringify(message));
            addMessage('user', `System command: ${command}`);
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const text = input.value.trim();
            
            if (!text) {
                addMessage('error', 'Please enter a message');
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('error', 'Not connected');
                return;
            }

            const message = {
                type: 'text_input',
                data: {
                    message: text,
                    context: {
                        preferred_agent: 'agent1_openrouter_gpt40',
                        user_settings: {
                            theme: 'dark',
                            voiceEnabled: true,
                            preferredAgent: 'agent1_openrouter_gpt40',
                            budgetLimit: 100,
                            autoPlayTTS: true,
                            voiceActivityDetection: true,
                            notifications: true,
                            language: 'en'
                        }
                    }
                },
                timestamp: new Date().toISOString(),
                session_id: sessionId
            };

            ws.send(JSON.stringify(message));
            addMessage('user', text);
            input.value = '';
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Auto-connect on page load
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html>
