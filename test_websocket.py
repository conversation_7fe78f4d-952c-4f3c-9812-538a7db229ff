#!/usr/bin/env python3
"""Simple WebSocket test client to verify frontend functionality."""

import asyncio
import json
import websockets

async def test_websocket():
    """Test WebSocket connection and message handling."""
    uri = "ws://localhost:8000/ws/test_session_123"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"Connected to {uri}")
            
            # Send a text message
            message = {
                "type": "text_input",
                "data": {
                    "message": "Hello from WebSocket test client!",
                    "context": {}
                },
                "timestamp": "2025-07-20T21:53:00Z",
                "session_id": "test_session_123"
            }
            
            print(f"Sending message: {json.dumps(message, indent=2)}")
            await websocket.send(json.dumps(message))
            
            # Wait for responses
            print("Waiting for responses...")
            responses_received = 0
            max_responses = 3  # Connection status + agent response + cost update
            
            while responses_received < max_responses:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                    print(f"\n--- Response {responses_received + 1} ---")
                    print(f"Raw response: {response}")
                    
                    # Parse and display response
                    response_data = json.loads(response)
                    print(f"Response type: {response_data.get('type')}")
                    print(f"Response data: {json.dumps(response_data.get('data', {}), indent=2)}")
                    
                    responses_received += 1
                    
                    # If we get an agent response, that's what we're mainly interested in
                    if response_data.get('type') == 'agent_response':
                        print(f"\n🎉 AGENT RESPONSE RECEIVED!")
                        print(f"Agent: {response_data.get('data', {}).get('agent_name', 'Unknown')}")
                        print(f"Message: {response_data.get('data', {}).get('message', 'No message')}")
                        break
                    
                except asyncio.TimeoutError:
                    print(f"Timeout waiting for response {responses_received + 1}")
                    break
            
    except websockets.exceptions.ConnectionClosed:
        print("WebSocket connection closed")
    except asyncio.TimeoutError:
        print("Timeout waiting for response")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())