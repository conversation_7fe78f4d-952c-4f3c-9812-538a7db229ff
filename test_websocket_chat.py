#!/usr/bin/env python3
"""
Test script for the refactored WebSocket chat system.
"""

import asyncio
import json
import websockets
import uuid
from datetime import datetime

async def test_websocket_chat():
    """Test the WebSocket chat communication."""
    
    # Generate a session ID
    session_id = f"test-session-{uuid.uuid4().hex[:8]}"
    websocket_url = f"ws://localhost:8000/ws/{session_id}"
    
    print(f"Testing WebSocket connection to: {websocket_url}")
    print(f"Session ID: {session_id}")
    
    try:
        async with websockets.connect(websocket_url) as websocket:
            print("✅ WebSocket connection established")
            
            # Send a text message to test the chat
            test_message = {
                "type": "text_input",
                "data": {
                    "message": "Hello, this is a test message from the refactored chat system!",
                    "context": {
                        "test": True,
                        "timestamp": datetime.now().isoformat()
                    }
                },
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id
            }
            
            print(f"📤 Sending test message: {test_message['data']['message']}")
            await websocket.send(json.dumps(test_message))
            print("✅ Message sent successfully")
            
            # Listen for responses
            print("👂 Listening for responses...")
            response_count = 0
            timeout_seconds = 30
            
            try:
                while response_count < 5:  # Listen for up to 5 responses
                    response = await asyncio.wait_for(websocket.recv(), timeout=timeout_seconds)
                    response_data = json.loads(response)
                    response_count += 1
                    
                    print(f"📥 Response {response_count}: {response_data['type']}")
                    
                    if response_data['type'] == 'agent_response':
                        print(f"   Agent: {response_data['data'].get('agent_name', 'Unknown')}")
                        print(f"   Message: {response_data['data'].get('message', 'No message')}")
                        print(f"   Cost: ${response_data['data'].get('cost', 0)}")
                        print("✅ Agent response received successfully!")
                        break
                    elif response_data['type'] == 'connection_status':
                        print(f"   Status: {response_data['data'].get('status', 'unknown')}")
                    elif response_data['type'] == 'cost_update':
                        print(f"   Session cost: ${response_data['data'].get('session_cost', 0)}")
                    elif response_data['type'] == 'error':
                        print(f"   Error: {response_data['data'].get('error_message', 'Unknown error')}")
                        print("❌ Error received from server")
                    else:
                        print(f"   Data: {json.dumps(response_data['data'], indent=2)}")
            
            except asyncio.TimeoutError:
                print(f"⏰ Timeout after {timeout_seconds} seconds")
                print("   This might indicate an issue with the agent service response")
            
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket connection closed: {e}")
    except Exception as e:
        print(f"❌ Error during WebSocket test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Starting WebSocket Chat Test")
    print("=" * 50)
    asyncio.run(test_websocket_chat())
    print("=" * 50)
    print("🏁 WebSocket Chat Test Completed")