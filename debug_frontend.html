<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .message { padding: 8px; margin: 5px 0; border-left: 3px solid #007bff; background-color: #f8f9fa; }
        .log { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9; }
        input, button { padding: 8px; margin: 5px; }
        button { background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Chat Debug Test</h1>
        
        <div id="status" class="status info">
            Status: Disconnected
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="Type your message..." style="width: 60%;">
            <button id="sendBtn" onclick="sendMessage()" disabled>Send Message</button>
            <button id="connectBtn" onclick="toggleConnection()">Connect</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        let sessionId = 'debug-session-' + Math.random().toString(36).substr(2, 9);
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const div = document.createElement('div');
            div.className = 'message';
            div.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logElement.appendChild(div);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = `Status: ${message}`;
            statusElement.className = `status ${type}`;
        }
        
        function toggleConnection() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                disconnect();
            } else {
                connect();
            }
        }
        
        function connect() {
            try {
                const wsUrl = `ws://127.0.0.1:8000/ws/${sessionId}`;
                log(`Connecting to: ${wsUrl}`);
                
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    log('✅ WebSocket connected successfully');
                    updateStatus('Connected', 'success');
                    document.getElementById('connectBtn').textContent = 'Disconnect';
                    document.getElementById('sendBtn').disabled = false;
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`📥 Received: ${data.type} - ${JSON.stringify(data.data, null, 2)}`);
                        
                        if (data.type === 'agent_response') {
                            log(`🤖 Agent Response: ${data.data.message}`, 'success');
                        } else if (data.type === 'error') {
                            log(`❌ Error: ${data.data.error_message}`, 'error');
                        }
                    } catch (e) {
                        log(`📥 Received (raw): ${event.data}`);
                    }
                };
                
                ws.onclose = function(event) {
                    log(`❌ WebSocket closed: Code ${event.code}, Reason: ${event.reason}`);
                    updateStatus('Disconnected', 'error');
                    document.getElementById('connectBtn').textContent = 'Connect';
                    document.getElementById('sendBtn').disabled = true;
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    updateStatus('Error', 'error');
                };
                
            } catch (error) {
                log(`❌ Connection error: ${error}`, 'error');
                updateStatus('Error', 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !ws || ws.readyState !== WebSocket.OPEN) {
                return;
            }
            
            const payload = {
                type: 'text_input',
                data: {
                    message: message,
                    context: {
                        test: true,
                        timestamp: new Date().toISOString()
                    }
                },
                timestamp: new Date().toISOString(),
                session_id: sessionId
            };
            
            try {
                ws.send(JSON.stringify(payload));
                log(`📤 Sent: ${message}`);
                input.value = '';
            } catch (error) {
                log(`❌ Send error: ${error}`, 'error');
            }
        }
        
        // Handle Enter key in input
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Auto-connect on load
        setTimeout(() => {
            log('🚀 Starting debug session...');
            connect();
        }, 1000);
    </script>
</body>
</html>