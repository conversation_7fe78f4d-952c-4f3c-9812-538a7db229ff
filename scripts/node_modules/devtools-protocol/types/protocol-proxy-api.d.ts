/**********************************************************************
 * Auto-generated by protocol-dts-generator.ts, do not edit manually. *
 **********************************************************************/

import Protocol from './protocol'

/**
 * API generated from Protocol commands and events.
 */
export namespace ProtocolProxyApi {

    export interface ProtocolApi {
        Console: ConsoleApi;

        Debugger: DebuggerApi;

        HeapProfiler: HeapProfilerApi;

        Profiler: ProfilerApi;

        Runtime: RuntimeApi;

        Schema: SchemaApi;

        Accessibility: AccessibilityApi;

        Animation: AnimationApi;

        Audits: AuditsApi;

        Extensions: ExtensionsApi;

        Autofill: AutofillApi;

        BackgroundService: BackgroundServiceApi;

        Browser: <PERSON>rowser<PERSON><PERSON>;

        CSS: CSSApi;

        CacheStorage: CacheS<PERSON>ageApi;

        Cast: <PERSON><PERSON><PERSON>;

        DOM: <PERSON><PERSON>Api;

        DOMDebugger: DOMDebuggerApi;

        EventBreakpoints: EventBreakpointsApi;

        DOMSnapshot: DOMSnapshotApi;

        DOMStorage: DOMStorageApi;

        DeviceOrientation: DeviceOrientationApi;

        Emulation: EmulationApi;

        HeadlessExperimental: HeadlessExperimentalApi;

        IO: IOApi;

        FileSystem: FileSystemApi;

        IndexedDB: IndexedDBApi;

        Input: InputApi;

        Inspector: InspectorApi;

        LayerTree: LayerTreeApi;

        Log: LogApi;

        Memory: MemoryApi;

        Network: NetworkApi;

        Overlay: OverlayApi;

        Page: PageApi;

        Performance: PerformanceApi;

        PerformanceTimeline: PerformanceTimelineApi;

        Security: SecurityApi;

        ServiceWorker: ServiceWorkerApi;

        Storage: StorageApi;

        SystemInfo: SystemInfoApi;

        Target: TargetApi;

        Tethering: TetheringApi;

        Tracing: TracingApi;

        Fetch: FetchApi;

        WebAudio: WebAudioApi;

        WebAuthn: WebAuthnApi;

        Media: MediaApi;

        DeviceAccess: DeviceAccessApi;

        Preload: PreloadApi;

        FedCm: FedCmApi;

        PWA: PWAApi;

        BluetoothEmulation: BluetoothEmulationApi;

    }


    export interface ConsoleApi {
        /**
         * Does nothing.
         */
        clearMessages(): Promise<void>;

        /**
         * Disables console domain, prevents further console messages from being reported to the client.
         */
        disable(): Promise<void>;

        /**
         * Enables console domain, sends the messages collected so far to the client by means of the
         * `messageAdded` notification.
         */
        enable(): Promise<void>;

        /**
         * Issued when new console message is added.
         */
        on(event: 'messageAdded', listener: (params: Protocol.Console.MessageAddedEvent) => void): void;

    }

    export interface DebuggerApi {
        /**
         * Continues execution until specific location is reached.
         */
        continueToLocation(params: Protocol.Debugger.ContinueToLocationRequest): Promise<void>;

        /**
         * Disables debugger for given page.
         */
        disable(): Promise<void>;

        /**
         * Enables debugger for the given page. Clients should not assume that the debugging has been
         * enabled until the result for this command is received.
         */
        enable(params: Protocol.Debugger.EnableRequest): Promise<Protocol.Debugger.EnableResponse>;

        /**
         * Evaluates expression on a given call frame.
         */
        evaluateOnCallFrame(params: Protocol.Debugger.EvaluateOnCallFrameRequest): Promise<Protocol.Debugger.EvaluateOnCallFrameResponse>;

        /**
         * Returns possible locations for breakpoint. scriptId in start and end range locations should be
         * the same.
         */
        getPossibleBreakpoints(params: Protocol.Debugger.GetPossibleBreakpointsRequest): Promise<Protocol.Debugger.GetPossibleBreakpointsResponse>;

        /**
         * Returns source for the script with given id.
         */
        getScriptSource(params: Protocol.Debugger.GetScriptSourceRequest): Promise<Protocol.Debugger.GetScriptSourceResponse>;

        disassembleWasmModule(params: Protocol.Debugger.DisassembleWasmModuleRequest): Promise<Protocol.Debugger.DisassembleWasmModuleResponse>;

        /**
         * Disassemble the next chunk of lines for the module corresponding to the
         * stream. If disassembly is complete, this API will invalidate the streamId
         * and return an empty chunk. Any subsequent calls for the now invalid stream
         * will return errors.
         */
        nextWasmDisassemblyChunk(params: Protocol.Debugger.NextWasmDisassemblyChunkRequest): Promise<Protocol.Debugger.NextWasmDisassemblyChunkResponse>;

        /**
         * This command is deprecated. Use getScriptSource instead.
         */
        getWasmBytecode(params: Protocol.Debugger.GetWasmBytecodeRequest): Promise<Protocol.Debugger.GetWasmBytecodeResponse>;

        /**
         * Returns stack trace with given `stackTraceId`.
         */
        getStackTrace(params: Protocol.Debugger.GetStackTraceRequest): Promise<Protocol.Debugger.GetStackTraceResponse>;

        /**
         * Stops on the next JavaScript statement.
         */
        pause(): Promise<void>;

        pauseOnAsyncCall(params: Protocol.Debugger.PauseOnAsyncCallRequest): Promise<void>;

        /**
         * Removes JavaScript breakpoint.
         */
        removeBreakpoint(params: Protocol.Debugger.RemoveBreakpointRequest): Promise<void>;

        /**
         * Restarts particular call frame from the beginning. The old, deprecated
         * behavior of `restartFrame` is to stay paused and allow further CDP commands
         * after a restart was scheduled. This can cause problems with restarting, so
         * we now continue execution immediatly after it has been scheduled until we
         * reach the beginning of the restarted frame.
         * 
         * To stay back-wards compatible, `restartFrame` now expects a `mode`
         * parameter to be present. If the `mode` parameter is missing, `restartFrame`
         * errors out.
         * 
         * The various return values are deprecated and `callFrames` is always empty.
         * Use the call frames from the `Debugger#paused` events instead, that fires
         * once V8 pauses at the beginning of the restarted function.
         */
        restartFrame(params: Protocol.Debugger.RestartFrameRequest): Promise<Protocol.Debugger.RestartFrameResponse>;

        /**
         * Resumes JavaScript execution.
         */
        resume(params: Protocol.Debugger.ResumeRequest): Promise<void>;

        /**
         * Searches for given string in script content.
         */
        searchInContent(params: Protocol.Debugger.SearchInContentRequest): Promise<Protocol.Debugger.SearchInContentResponse>;

        /**
         * Enables or disables async call stacks tracking.
         */
        setAsyncCallStackDepth(params: Protocol.Debugger.SetAsyncCallStackDepthRequest): Promise<void>;

        /**
         * Replace previous blackbox execution contexts with passed ones. Forces backend to skip
         * stepping/pausing in scripts in these execution contexts. VM will try to leave blackboxed script by
         * performing 'step in' several times, finally resorting to 'step out' if unsuccessful.
         */
        setBlackboxExecutionContexts(params: Protocol.Debugger.SetBlackboxExecutionContextsRequest): Promise<void>;

        /**
         * Replace previous blackbox patterns with passed ones. Forces backend to skip stepping/pausing in
         * scripts with url matching one of the patterns. VM will try to leave blackboxed script by
         * performing 'step in' several times, finally resorting to 'step out' if unsuccessful.
         */
        setBlackboxPatterns(params: Protocol.Debugger.SetBlackboxPatternsRequest): Promise<void>;

        /**
         * Makes backend skip steps in the script in blackboxed ranges. VM will try leave blacklisted
         * scripts by performing 'step in' several times, finally resorting to 'step out' if unsuccessful.
         * Positions array contains positions where blackbox state is changed. First interval isn't
         * blackboxed. Array should be sorted.
         */
        setBlackboxedRanges(params: Protocol.Debugger.SetBlackboxedRangesRequest): Promise<void>;

        /**
         * Sets JavaScript breakpoint at a given location.
         */
        setBreakpoint(params: Protocol.Debugger.SetBreakpointRequest): Promise<Protocol.Debugger.SetBreakpointResponse>;

        /**
         * Sets instrumentation breakpoint.
         */
        setInstrumentationBreakpoint(params: Protocol.Debugger.SetInstrumentationBreakpointRequest): Promise<Protocol.Debugger.SetInstrumentationBreakpointResponse>;

        /**
         * Sets JavaScript breakpoint at given location specified either by URL or URL regex. Once this
         * command is issued, all existing parsed scripts will have breakpoints resolved and returned in
         * `locations` property. Further matching script parsing will result in subsequent
         * `breakpointResolved` events issued. This logical breakpoint will survive page reloads.
         */
        setBreakpointByUrl(params: Protocol.Debugger.SetBreakpointByUrlRequest): Promise<Protocol.Debugger.SetBreakpointByUrlResponse>;

        /**
         * Sets JavaScript breakpoint before each call to the given function.
         * If another function was created from the same source as a given one,
         * calling it will also trigger the breakpoint.
         */
        setBreakpointOnFunctionCall(params: Protocol.Debugger.SetBreakpointOnFunctionCallRequest): Promise<Protocol.Debugger.SetBreakpointOnFunctionCallResponse>;

        /**
         * Activates / deactivates all breakpoints on the page.
         */
        setBreakpointsActive(params: Protocol.Debugger.SetBreakpointsActiveRequest): Promise<void>;

        /**
         * Defines pause on exceptions state. Can be set to stop on all exceptions, uncaught exceptions,
         * or caught exceptions, no exceptions. Initial pause on exceptions state is `none`.
         */
        setPauseOnExceptions(params: Protocol.Debugger.SetPauseOnExceptionsRequest): Promise<void>;

        /**
         * Changes return value in top frame. Available only at return break position.
         */
        setReturnValue(params: Protocol.Debugger.SetReturnValueRequest): Promise<void>;

        /**
         * Edits JavaScript source live.
         * 
         * In general, functions that are currently on the stack can not be edited with
         * a single exception: If the edited function is the top-most stack frame and
         * that is the only activation of that function on the stack. In this case
         * the live edit will be successful and a `Debugger.restartFrame` for the
         * top-most function is automatically triggered.
         */
        setScriptSource(params: Protocol.Debugger.SetScriptSourceRequest): Promise<Protocol.Debugger.SetScriptSourceResponse>;

        /**
         * Makes page not interrupt on any pauses (breakpoint, exception, dom exception etc).
         */
        setSkipAllPauses(params: Protocol.Debugger.SetSkipAllPausesRequest): Promise<void>;

        /**
         * Changes value of variable in a callframe. Object-based scopes are not supported and must be
         * mutated manually.
         */
        setVariableValue(params: Protocol.Debugger.SetVariableValueRequest): Promise<void>;

        /**
         * Steps into the function call.
         */
        stepInto(params: Protocol.Debugger.StepIntoRequest): Promise<void>;

        /**
         * Steps out of the function call.
         */
        stepOut(): Promise<void>;

        /**
         * Steps over the statement.
         */
        stepOver(params: Protocol.Debugger.StepOverRequest): Promise<void>;

        /**
         * Fired when breakpoint is resolved to an actual script and location.
         * Deprecated in favor of `resolvedBreakpoints` in the `scriptParsed` event.
         */
        on(event: 'breakpointResolved', listener: (params: Protocol.Debugger.BreakpointResolvedEvent) => void): void;

        /**
         * Fired when the virtual machine stopped on breakpoint or exception or any other stop criteria.
         */
        on(event: 'paused', listener: (params: Protocol.Debugger.PausedEvent) => void): void;

        /**
         * Fired when the virtual machine resumed execution.
         */
        on(event: 'resumed', listener: () => void): void;

        /**
         * Fired when virtual machine fails to parse the script.
         */
        on(event: 'scriptFailedToParse', listener: (params: Protocol.Debugger.ScriptFailedToParseEvent) => void): void;

        /**
         * Fired when virtual machine parses script. This event is also fired for all known and uncollected
         * scripts upon enabling debugger.
         */
        on(event: 'scriptParsed', listener: (params: Protocol.Debugger.ScriptParsedEvent) => void): void;

    }

    export interface HeapProfilerApi {
        /**
         * Enables console to refer to the node with given id via $x (see Command Line API for more details
         * $x functions).
         */
        addInspectedHeapObject(params: Protocol.HeapProfiler.AddInspectedHeapObjectRequest): Promise<void>;

        collectGarbage(): Promise<void>;

        disable(): Promise<void>;

        enable(): Promise<void>;

        getHeapObjectId(params: Protocol.HeapProfiler.GetHeapObjectIdRequest): Promise<Protocol.HeapProfiler.GetHeapObjectIdResponse>;

        getObjectByHeapObjectId(params: Protocol.HeapProfiler.GetObjectByHeapObjectIdRequest): Promise<Protocol.HeapProfiler.GetObjectByHeapObjectIdResponse>;

        getSamplingProfile(): Promise<Protocol.HeapProfiler.GetSamplingProfileResponse>;

        startSampling(params: Protocol.HeapProfiler.StartSamplingRequest): Promise<void>;

        startTrackingHeapObjects(params: Protocol.HeapProfiler.StartTrackingHeapObjectsRequest): Promise<void>;

        stopSampling(): Promise<Protocol.HeapProfiler.StopSamplingResponse>;

        stopTrackingHeapObjects(params: Protocol.HeapProfiler.StopTrackingHeapObjectsRequest): Promise<void>;

        takeHeapSnapshot(params: Protocol.HeapProfiler.TakeHeapSnapshotRequest): Promise<void>;

        on(event: 'addHeapSnapshotChunk', listener: (params: Protocol.HeapProfiler.AddHeapSnapshotChunkEvent) => void): void;

        /**
         * If heap objects tracking has been started then backend may send update for one or more fragments
         */
        on(event: 'heapStatsUpdate', listener: (params: Protocol.HeapProfiler.HeapStatsUpdateEvent) => void): void;

        /**
         * If heap objects tracking has been started then backend regularly sends a current value for last
         * seen object id and corresponding timestamp. If the were changes in the heap since last event
         * then one or more heapStatsUpdate events will be sent before a new lastSeenObjectId event.
         */
        on(event: 'lastSeenObjectId', listener: (params: Protocol.HeapProfiler.LastSeenObjectIdEvent) => void): void;

        on(event: 'reportHeapSnapshotProgress', listener: (params: Protocol.HeapProfiler.ReportHeapSnapshotProgressEvent) => void): void;

        on(event: 'resetProfiles', listener: () => void): void;

    }

    export interface ProfilerApi {
        disable(): Promise<void>;

        enable(): Promise<void>;

        /**
         * Collect coverage data for the current isolate. The coverage data may be incomplete due to
         * garbage collection.
         */
        getBestEffortCoverage(): Promise<Protocol.Profiler.GetBestEffortCoverageResponse>;

        /**
         * Changes CPU profiler sampling interval. Must be called before CPU profiles recording started.
         */
        setSamplingInterval(params: Protocol.Profiler.SetSamplingIntervalRequest): Promise<void>;

        start(): Promise<void>;

        /**
         * Enable precise code coverage. Coverage data for JavaScript executed before enabling precise code
         * coverage may be incomplete. Enabling prevents running optimized code and resets execution
         * counters.
         */
        startPreciseCoverage(params: Protocol.Profiler.StartPreciseCoverageRequest): Promise<Protocol.Profiler.StartPreciseCoverageResponse>;

        stop(): Promise<Protocol.Profiler.StopResponse>;

        /**
         * Disable precise code coverage. Disabling releases unnecessary execution count records and allows
         * executing optimized code.
         */
        stopPreciseCoverage(): Promise<void>;

        /**
         * Collect coverage data for the current isolate, and resets execution counters. Precise code
         * coverage needs to have started.
         */
        takePreciseCoverage(): Promise<Protocol.Profiler.TakePreciseCoverageResponse>;

        on(event: 'consoleProfileFinished', listener: (params: Protocol.Profiler.ConsoleProfileFinishedEvent) => void): void;

        /**
         * Sent when new profile recording is started using console.profile() call.
         */
        on(event: 'consoleProfileStarted', listener: (params: Protocol.Profiler.ConsoleProfileStartedEvent) => void): void;

        /**
         * Reports coverage delta since the last poll (either from an event like this, or from
         * `takePreciseCoverage` for the current isolate. May only be sent if precise code
         * coverage has been started. This event can be trigged by the embedder to, for example,
         * trigger collection of coverage data immediately at a certain point in time.
         */
        on(event: 'preciseCoverageDeltaUpdate', listener: (params: Protocol.Profiler.PreciseCoverageDeltaUpdateEvent) => void): void;

    }

    export interface RuntimeApi {
        /**
         * Add handler to promise with given promise object id.
         */
        awaitPromise(params: Protocol.Runtime.AwaitPromiseRequest): Promise<Protocol.Runtime.AwaitPromiseResponse>;

        /**
         * Calls function with given declaration on the given object. Object group of the result is
         * inherited from the target object.
         */
        callFunctionOn(params: Protocol.Runtime.CallFunctionOnRequest): Promise<Protocol.Runtime.CallFunctionOnResponse>;

        /**
         * Compiles expression.
         */
        compileScript(params: Protocol.Runtime.CompileScriptRequest): Promise<Protocol.Runtime.CompileScriptResponse>;

        /**
         * Disables reporting of execution contexts creation.
         */
        disable(): Promise<void>;

        /**
         * Discards collected exceptions and console API calls.
         */
        discardConsoleEntries(): Promise<void>;

        /**
         * Enables reporting of execution contexts creation by means of `executionContextCreated` event.
         * When the reporting gets enabled the event will be sent immediately for each existing execution
         * context.
         */
        enable(): Promise<void>;

        /**
         * Evaluates expression on global object.
         */
        evaluate(params: Protocol.Runtime.EvaluateRequest): Promise<Protocol.Runtime.EvaluateResponse>;

        /**
         * Returns the isolate id.
         */
        getIsolateId(): Promise<Protocol.Runtime.GetIsolateIdResponse>;

        /**
         * Returns the JavaScript heap usage.
         * It is the total usage of the corresponding isolate not scoped to a particular Runtime.
         */
        getHeapUsage(): Promise<Protocol.Runtime.GetHeapUsageResponse>;

        /**
         * Returns properties of a given object. Object group of the result is inherited from the target
         * object.
         */
        getProperties(params: Protocol.Runtime.GetPropertiesRequest): Promise<Protocol.Runtime.GetPropertiesResponse>;

        /**
         * Returns all let, const and class variables from global scope.
         */
        globalLexicalScopeNames(params: Protocol.Runtime.GlobalLexicalScopeNamesRequest): Promise<Protocol.Runtime.GlobalLexicalScopeNamesResponse>;

        queryObjects(params: Protocol.Runtime.QueryObjectsRequest): Promise<Protocol.Runtime.QueryObjectsResponse>;

        /**
         * Releases remote object with given id.
         */
        releaseObject(params: Protocol.Runtime.ReleaseObjectRequest): Promise<void>;

        /**
         * Releases all remote objects that belong to a given group.
         */
        releaseObjectGroup(params: Protocol.Runtime.ReleaseObjectGroupRequest): Promise<void>;

        /**
         * Tells inspected instance to run if it was waiting for debugger to attach.
         */
        runIfWaitingForDebugger(): Promise<void>;

        /**
         * Runs script with given id in a given context.
         */
        runScript(params: Protocol.Runtime.RunScriptRequest): Promise<Protocol.Runtime.RunScriptResponse>;

        /**
         * Enables or disables async call stacks tracking.
         */
        setAsyncCallStackDepth(params: Protocol.Runtime.SetAsyncCallStackDepthRequest): Promise<void>;

        setCustomObjectFormatterEnabled(params: Protocol.Runtime.SetCustomObjectFormatterEnabledRequest): Promise<void>;

        setMaxCallStackSizeToCapture(params: Protocol.Runtime.SetMaxCallStackSizeToCaptureRequest): Promise<void>;

        /**
         * Terminate current or next JavaScript execution.
         * Will cancel the termination when the outer-most script execution ends.
         */
        terminateExecution(): Promise<void>;

        /**
         * If executionContextId is empty, adds binding with the given name on the
         * global objects of all inspected contexts, including those created later,
         * bindings survive reloads.
         * Binding function takes exactly one argument, this argument should be string,
         * in case of any other input, function throws an exception.
         * Each binding function call produces Runtime.bindingCalled notification.
         */
        addBinding(params: Protocol.Runtime.AddBindingRequest): Promise<void>;

        /**
         * This method does not remove binding function from global object but
         * unsubscribes current runtime agent from Runtime.bindingCalled notifications.
         */
        removeBinding(params: Protocol.Runtime.RemoveBindingRequest): Promise<void>;

        /**
         * This method tries to lookup and populate exception details for a
         * JavaScript Error object.
         * Note that the stackTrace portion of the resulting exceptionDetails will
         * only be populated if the Runtime domain was enabled at the time when the
         * Error was thrown.
         */
        getExceptionDetails(params: Protocol.Runtime.GetExceptionDetailsRequest): Promise<Protocol.Runtime.GetExceptionDetailsResponse>;

        /**
         * Notification is issued every time when binding is called.
         */
        on(event: 'bindingCalled', listener: (params: Protocol.Runtime.BindingCalledEvent) => void): void;

        /**
         * Issued when console API was called.
         */
        on(event: 'consoleAPICalled', listener: (params: Protocol.Runtime.ConsoleAPICalledEvent) => void): void;

        /**
         * Issued when unhandled exception was revoked.
         */
        on(event: 'exceptionRevoked', listener: (params: Protocol.Runtime.ExceptionRevokedEvent) => void): void;

        /**
         * Issued when exception was thrown and unhandled.
         */
        on(event: 'exceptionThrown', listener: (params: Protocol.Runtime.ExceptionThrownEvent) => void): void;

        /**
         * Issued when new execution context is created.
         */
        on(event: 'executionContextCreated', listener: (params: Protocol.Runtime.ExecutionContextCreatedEvent) => void): void;

        /**
         * Issued when execution context is destroyed.
         */
        on(event: 'executionContextDestroyed', listener: (params: Protocol.Runtime.ExecutionContextDestroyedEvent) => void): void;

        /**
         * Issued when all executionContexts were cleared in browser
         */
        on(event: 'executionContextsCleared', listener: () => void): void;

        /**
         * Issued when object should be inspected (for example, as a result of inspect() command line API
         * call).
         */
        on(event: 'inspectRequested', listener: (params: Protocol.Runtime.InspectRequestedEvent) => void): void;

    }

    export interface SchemaApi {
        /**
         * Returns supported domains.
         */
        getDomains(): Promise<Protocol.Schema.GetDomainsResponse>;

    }

    export interface AccessibilityApi {
        /**
         * Disables the accessibility domain.
         */
        disable(): Promise<void>;

        /**
         * Enables the accessibility domain which causes `AXNodeId`s to remain consistent between method calls.
         * This turns on accessibility for the page, which can impact performance until accessibility is disabled.
         */
        enable(): Promise<void>;

        /**
         * Fetches the accessibility node and partial accessibility tree for this DOM node, if it exists.
         */
        getPartialAXTree(params: Protocol.Accessibility.GetPartialAXTreeRequest): Promise<Protocol.Accessibility.GetPartialAXTreeResponse>;

        /**
         * Fetches the entire accessibility tree for the root Document
         */
        getFullAXTree(params: Protocol.Accessibility.GetFullAXTreeRequest): Promise<Protocol.Accessibility.GetFullAXTreeResponse>;

        /**
         * Fetches the root node.
         * Requires `enable()` to have been called previously.
         */
        getRootAXNode(params: Protocol.Accessibility.GetRootAXNodeRequest): Promise<Protocol.Accessibility.GetRootAXNodeResponse>;

        /**
         * Fetches a node and all ancestors up to and including the root.
         * Requires `enable()` to have been called previously.
         */
        getAXNodeAndAncestors(params: Protocol.Accessibility.GetAXNodeAndAncestorsRequest): Promise<Protocol.Accessibility.GetAXNodeAndAncestorsResponse>;

        /**
         * Fetches a particular accessibility node by AXNodeId.
         * Requires `enable()` to have been called previously.
         */
        getChildAXNodes(params: Protocol.Accessibility.GetChildAXNodesRequest): Promise<Protocol.Accessibility.GetChildAXNodesResponse>;

        /**
         * Query a DOM node's accessibility subtree for accessible name and role.
         * This command computes the name and role for all nodes in the subtree, including those that are
         * ignored for accessibility, and returns those that match the specified name and role. If no DOM
         * node is specified, or the DOM node does not exist, the command returns an error. If neither
         * `accessibleName` or `role` is specified, it returns all the accessibility nodes in the subtree.
         */
        queryAXTree(params: Protocol.Accessibility.QueryAXTreeRequest): Promise<Protocol.Accessibility.QueryAXTreeResponse>;

        /**
         * The loadComplete event mirrors the load complete event sent by the browser to assistive
         * technology when the web page has finished loading.
         */
        on(event: 'loadComplete', listener: (params: Protocol.Accessibility.LoadCompleteEvent) => void): void;

        /**
         * The nodesUpdated event is sent every time a previously requested node has changed the in tree.
         */
        on(event: 'nodesUpdated', listener: (params: Protocol.Accessibility.NodesUpdatedEvent) => void): void;

    }

    export interface AnimationApi {
        /**
         * Disables animation domain notifications.
         */
        disable(): Promise<void>;

        /**
         * Enables animation domain notifications.
         */
        enable(): Promise<void>;

        /**
         * Returns the current time of the an animation.
         */
        getCurrentTime(params: Protocol.Animation.GetCurrentTimeRequest): Promise<Protocol.Animation.GetCurrentTimeResponse>;

        /**
         * Gets the playback rate of the document timeline.
         */
        getPlaybackRate(): Promise<Protocol.Animation.GetPlaybackRateResponse>;

        /**
         * Releases a set of animations to no longer be manipulated.
         */
        releaseAnimations(params: Protocol.Animation.ReleaseAnimationsRequest): Promise<void>;

        /**
         * Gets the remote object of the Animation.
         */
        resolveAnimation(params: Protocol.Animation.ResolveAnimationRequest): Promise<Protocol.Animation.ResolveAnimationResponse>;

        /**
         * Seek a set of animations to a particular time within each animation.
         */
        seekAnimations(params: Protocol.Animation.SeekAnimationsRequest): Promise<void>;

        /**
         * Sets the paused state of a set of animations.
         */
        setPaused(params: Protocol.Animation.SetPausedRequest): Promise<void>;

        /**
         * Sets the playback rate of the document timeline.
         */
        setPlaybackRate(params: Protocol.Animation.SetPlaybackRateRequest): Promise<void>;

        /**
         * Sets the timing of an animation node.
         */
        setTiming(params: Protocol.Animation.SetTimingRequest): Promise<void>;

        /**
         * Event for when an animation has been cancelled.
         */
        on(event: 'animationCanceled', listener: (params: Protocol.Animation.AnimationCanceledEvent) => void): void;

        /**
         * Event for each animation that has been created.
         */
        on(event: 'animationCreated', listener: (params: Protocol.Animation.AnimationCreatedEvent) => void): void;

        /**
         * Event for animation that has been started.
         */
        on(event: 'animationStarted', listener: (params: Protocol.Animation.AnimationStartedEvent) => void): void;

        /**
         * Event for animation that has been updated.
         */
        on(event: 'animationUpdated', listener: (params: Protocol.Animation.AnimationUpdatedEvent) => void): void;

    }

    export interface AuditsApi {
        /**
         * Returns the response body and size if it were re-encoded with the specified settings. Only
         * applies to images.
         */
        getEncodedResponse(params: Protocol.Audits.GetEncodedResponseRequest): Promise<Protocol.Audits.GetEncodedResponseResponse>;

        /**
         * Disables issues domain, prevents further issues from being reported to the client.
         */
        disable(): Promise<void>;

        /**
         * Enables issues domain, sends the issues collected so far to the client by means of the
         * `issueAdded` event.
         */
        enable(): Promise<void>;

        /**
         * Runs the contrast check for the target page. Found issues are reported
         * using Audits.issueAdded event.
         */
        checkContrast(params: Protocol.Audits.CheckContrastRequest): Promise<void>;

        /**
         * Runs the form issues check for the target page. Found issues are reported
         * using Audits.issueAdded event.
         */
        checkFormsIssues(): Promise<Protocol.Audits.CheckFormsIssuesResponse>;

        on(event: 'issueAdded', listener: (params: Protocol.Audits.IssueAddedEvent) => void): void;

    }

    export interface ExtensionsApi {
        /**
         * Installs an unpacked extension from the filesystem similar to
         * --load-extension CLI flags. Returns extension ID once the extension
         * has been installed. Available if the client is connected using the
         * --remote-debugging-pipe flag and the --enable-unsafe-extension-debugging
         * flag is set.
         */
        loadUnpacked(params: Protocol.Extensions.LoadUnpackedRequest): Promise<Protocol.Extensions.LoadUnpackedResponse>;

        /**
         * Uninstalls an unpacked extension (others not supported) from the profile.
         * Available if the client is connected using the --remote-debugging-pipe flag
         * and the --enable-unsafe-extension-debugging.
         */
        uninstall(params: Protocol.Extensions.UninstallRequest): Promise<void>;

        /**
         * Gets data from extension storage in the given `storageArea`. If `keys` is
         * specified, these are used to filter the result.
         */
        getStorageItems(params: Protocol.Extensions.GetStorageItemsRequest): Promise<Protocol.Extensions.GetStorageItemsResponse>;

        /**
         * Removes `keys` from extension storage in the given `storageArea`.
         */
        removeStorageItems(params: Protocol.Extensions.RemoveStorageItemsRequest): Promise<void>;

        /**
         * Clears extension storage in the given `storageArea`.
         */
        clearStorageItems(params: Protocol.Extensions.ClearStorageItemsRequest): Promise<void>;

        /**
         * Sets `values` in extension storage in the given `storageArea`. The provided `values`
         * will be merged with existing values in the storage area.
         */
        setStorageItems(params: Protocol.Extensions.SetStorageItemsRequest): Promise<void>;

    }

    export interface AutofillApi {
        /**
         * Trigger autofill on a form identified by the fieldId.
         * If the field and related form cannot be autofilled, returns an error.
         */
        trigger(params: Protocol.Autofill.TriggerRequest): Promise<void>;

        /**
         * Set addresses so that developers can verify their forms implementation.
         */
        setAddresses(params: Protocol.Autofill.SetAddressesRequest): Promise<void>;

        /**
         * Disables autofill domain notifications.
         */
        disable(): Promise<void>;

        /**
         * Enables autofill domain notifications.
         */
        enable(): Promise<void>;

        /**
         * Emitted when an address form is filled.
         */
        on(event: 'addressFormFilled', listener: (params: Protocol.Autofill.AddressFormFilledEvent) => void): void;

    }

    export interface BackgroundServiceApi {
        /**
         * Enables event updates for the service.
         */
        startObserving(params: Protocol.BackgroundService.StartObservingRequest): Promise<void>;

        /**
         * Disables event updates for the service.
         */
        stopObserving(params: Protocol.BackgroundService.StopObservingRequest): Promise<void>;

        /**
         * Set the recording state for the service.
         */
        setRecording(params: Protocol.BackgroundService.SetRecordingRequest): Promise<void>;

        /**
         * Clears all stored data for the service.
         */
        clearEvents(params: Protocol.BackgroundService.ClearEventsRequest): Promise<void>;

        /**
         * Called when the recording state for the service has been updated.
         */
        on(event: 'recordingStateChanged', listener: (params: Protocol.BackgroundService.RecordingStateChangedEvent) => void): void;

        /**
         * Called with all existing backgroundServiceEvents when enabled, and all new
         * events afterwards if enabled and recording.
         */
        on(event: 'backgroundServiceEventReceived', listener: (params: Protocol.BackgroundService.BackgroundServiceEventReceivedEvent) => void): void;

    }

    export interface BrowserApi {
        /**
         * Set permission settings for given origin.
         */
        setPermission(params: Protocol.Browser.SetPermissionRequest): Promise<void>;

        /**
         * Grant specific permissions to the given origin and reject all others.
         */
        grantPermissions(params: Protocol.Browser.GrantPermissionsRequest): Promise<void>;

        /**
         * Reset all permission management for all origins.
         */
        resetPermissions(params: Protocol.Browser.ResetPermissionsRequest): Promise<void>;

        /**
         * Set the behavior when downloading a file.
         */
        setDownloadBehavior(params: Protocol.Browser.SetDownloadBehaviorRequest): Promise<void>;

        /**
         * Cancel a download if in progress
         */
        cancelDownload(params: Protocol.Browser.CancelDownloadRequest): Promise<void>;

        /**
         * Close browser gracefully.
         */
        close(): Promise<void>;

        /**
         * Crashes browser on the main thread.
         */
        crash(): Promise<void>;

        /**
         * Crashes GPU process.
         */
        crashGpuProcess(): Promise<void>;

        /**
         * Returns version information.
         */
        getVersion(): Promise<Protocol.Browser.GetVersionResponse>;

        /**
         * Returns the command line switches for the browser process if, and only if
         * --enable-automation is on the commandline.
         */
        getBrowserCommandLine(): Promise<Protocol.Browser.GetBrowserCommandLineResponse>;

        /**
         * Get Chrome histograms.
         */
        getHistograms(params: Protocol.Browser.GetHistogramsRequest): Promise<Protocol.Browser.GetHistogramsResponse>;

        /**
         * Get a Chrome histogram by name.
         */
        getHistogram(params: Protocol.Browser.GetHistogramRequest): Promise<Protocol.Browser.GetHistogramResponse>;

        /**
         * Get position and size of the browser window.
         */
        getWindowBounds(params: Protocol.Browser.GetWindowBoundsRequest): Promise<Protocol.Browser.GetWindowBoundsResponse>;

        /**
         * Get the browser window that contains the devtools target.
         */
        getWindowForTarget(params: Protocol.Browser.GetWindowForTargetRequest): Promise<Protocol.Browser.GetWindowForTargetResponse>;

        /**
         * Set position and/or size of the browser window.
         */
        setWindowBounds(params: Protocol.Browser.SetWindowBoundsRequest): Promise<void>;

        /**
         * Set dock tile details, platform-specific.
         */
        setDockTile(params: Protocol.Browser.SetDockTileRequest): Promise<void>;

        /**
         * Invoke custom browser commands used by telemetry.
         */
        executeBrowserCommand(params: Protocol.Browser.ExecuteBrowserCommandRequest): Promise<void>;

        /**
         * Allows a site to use privacy sandbox features that require enrollment
         * without the site actually being enrolled. Only supported on page targets.
         */
        addPrivacySandboxEnrollmentOverride(params: Protocol.Browser.AddPrivacySandboxEnrollmentOverrideRequest): Promise<void>;

        /**
         * Configures encryption keys used with a given privacy sandbox API to talk
         * to a trusted coordinator.  Since this is intended for test automation only,
         * coordinatorOrigin must be a .test domain. No existing coordinator
         * configuration for the origin may exist.
         */
        addPrivacySandboxCoordinatorKeyConfig(params: Protocol.Browser.AddPrivacySandboxCoordinatorKeyConfigRequest): Promise<void>;

        /**
         * Fired when page is about to start a download.
         */
        on(event: 'downloadWillBegin', listener: (params: Protocol.Browser.DownloadWillBeginEvent) => void): void;

        /**
         * Fired when download makes progress. Last call has |done| == true.
         */
        on(event: 'downloadProgress', listener: (params: Protocol.Browser.DownloadProgressEvent) => void): void;

    }

    export interface CSSApi {
        /**
         * Inserts a new rule with the given `ruleText` in a stylesheet with given `styleSheetId`, at the
         * position specified by `location`.
         */
        addRule(params: Protocol.CSS.AddRuleRequest): Promise<Protocol.CSS.AddRuleResponse>;

        /**
         * Returns all class names from specified stylesheet.
         */
        collectClassNames(params: Protocol.CSS.CollectClassNamesRequest): Promise<Protocol.CSS.CollectClassNamesResponse>;

        /**
         * Creates a new special "via-inspector" stylesheet in the frame with given `frameId`.
         */
        createStyleSheet(params: Protocol.CSS.CreateStyleSheetRequest): Promise<Protocol.CSS.CreateStyleSheetResponse>;

        /**
         * Disables the CSS agent for the given page.
         */
        disable(): Promise<void>;

        /**
         * Enables the CSS agent for the given page. Clients should not assume that the CSS agent has been
         * enabled until the result of this command is received.
         */
        enable(): Promise<void>;

        /**
         * Ensures that the given node will have specified pseudo-classes whenever its style is computed by
         * the browser.
         */
        forcePseudoState(params: Protocol.CSS.ForcePseudoStateRequest): Promise<void>;

        /**
         * Ensures that the given node is in its starting-style state.
         */
        forceStartingStyle(params: Protocol.CSS.ForceStartingStyleRequest): Promise<void>;

        getBackgroundColors(params: Protocol.CSS.GetBackgroundColorsRequest): Promise<Protocol.CSS.GetBackgroundColorsResponse>;

        /**
         * Returns the computed style for a DOM node identified by `nodeId`.
         */
        getComputedStyleForNode(params: Protocol.CSS.GetComputedStyleForNodeRequest): Promise<Protocol.CSS.GetComputedStyleForNodeResponse>;

        /**
         * Resolve the specified values in the context of the provided element.
         * For example, a value of '1em' is evaluated according to the computed
         * 'font-size' of the element and a value 'calc(1px + 2px)' will be
         * resolved to '3px'.
         * If the `propertyName` was specified the `values` are resolved as if
         * they were property's declaration. If a value cannot be parsed according
         * to the provided property syntax, the value is parsed using combined
         * syntax as if null `propertyName` was provided. If the value cannot be
         * resolved even then, return the provided value without any changes.
         */
        resolveValues(params: Protocol.CSS.ResolveValuesRequest): Promise<Protocol.CSS.ResolveValuesResponse>;

        getLonghandProperties(params: Protocol.CSS.GetLonghandPropertiesRequest): Promise<Protocol.CSS.GetLonghandPropertiesResponse>;

        /**
         * Returns the styles defined inline (explicitly in the "style" attribute and implicitly, using DOM
         * attributes) for a DOM node identified by `nodeId`.
         */
        getInlineStylesForNode(params: Protocol.CSS.GetInlineStylesForNodeRequest): Promise<Protocol.CSS.GetInlineStylesForNodeResponse>;

        /**
         * Returns the styles coming from animations & transitions
         * including the animation & transition styles coming from inheritance chain.
         */
        getAnimatedStylesForNode(params: Protocol.CSS.GetAnimatedStylesForNodeRequest): Promise<Protocol.CSS.GetAnimatedStylesForNodeResponse>;

        /**
         * Returns requested styles for a DOM node identified by `nodeId`.
         */
        getMatchedStylesForNode(params: Protocol.CSS.GetMatchedStylesForNodeRequest): Promise<Protocol.CSS.GetMatchedStylesForNodeResponse>;

        /**
         * Returns all media queries parsed by the rendering engine.
         */
        getMediaQueries(): Promise<Protocol.CSS.GetMediaQueriesResponse>;

        /**
         * Requests information about platform fonts which we used to render child TextNodes in the given
         * node.
         */
        getPlatformFontsForNode(params: Protocol.CSS.GetPlatformFontsForNodeRequest): Promise<Protocol.CSS.GetPlatformFontsForNodeResponse>;

        /**
         * Returns the current textual content for a stylesheet.
         */
        getStyleSheetText(params: Protocol.CSS.GetStyleSheetTextRequest): Promise<Protocol.CSS.GetStyleSheetTextResponse>;

        /**
         * Returns all layers parsed by the rendering engine for the tree scope of a node.
         * Given a DOM element identified by nodeId, getLayersForNode returns the root
         * layer for the nearest ancestor document or shadow root. The layer root contains
         * the full layer tree for the tree scope and their ordering.
         */
        getLayersForNode(params: Protocol.CSS.GetLayersForNodeRequest): Promise<Protocol.CSS.GetLayersForNodeResponse>;

        /**
         * Given a CSS selector text and a style sheet ID, getLocationForSelector
         * returns an array of locations of the CSS selector in the style sheet.
         */
        getLocationForSelector(params: Protocol.CSS.GetLocationForSelectorRequest): Promise<Protocol.CSS.GetLocationForSelectorResponse>;

        /**
         * Starts tracking the given node for the computed style updates
         * and whenever the computed style is updated for node, it queues
         * a `computedStyleUpdated` event with throttling.
         * There can only be 1 node tracked for computed style updates
         * so passing a new node id removes tracking from the previous node.
         * Pass `undefined` to disable tracking.
         */
        trackComputedStyleUpdatesForNode(params: Protocol.CSS.TrackComputedStyleUpdatesForNodeRequest): Promise<void>;

        /**
         * Starts tracking the given computed styles for updates. The specified array of properties
         * replaces the one previously specified. Pass empty array to disable tracking.
         * Use takeComputedStyleUpdates to retrieve the list of nodes that had properties modified.
         * The changes to computed style properties are only tracked for nodes pushed to the front-end
         * by the DOM agent. If no changes to the tracked properties occur after the node has been pushed
         * to the front-end, no updates will be issued for the node.
         */
        trackComputedStyleUpdates(params: Protocol.CSS.TrackComputedStyleUpdatesRequest): Promise<void>;

        /**
         * Polls the next batch of computed style updates.
         */
        takeComputedStyleUpdates(): Promise<Protocol.CSS.TakeComputedStyleUpdatesResponse>;

        /**
         * Find a rule with the given active property for the given node and set the new value for this
         * property
         */
        setEffectivePropertyValueForNode(params: Protocol.CSS.SetEffectivePropertyValueForNodeRequest): Promise<void>;

        /**
         * Modifies the property rule property name.
         */
        setPropertyRulePropertyName(params: Protocol.CSS.SetPropertyRulePropertyNameRequest): Promise<Protocol.CSS.SetPropertyRulePropertyNameResponse>;

        /**
         * Modifies the keyframe rule key text.
         */
        setKeyframeKey(params: Protocol.CSS.SetKeyframeKeyRequest): Promise<Protocol.CSS.SetKeyframeKeyResponse>;

        /**
         * Modifies the rule selector.
         */
        setMediaText(params: Protocol.CSS.SetMediaTextRequest): Promise<Protocol.CSS.SetMediaTextResponse>;

        /**
         * Modifies the expression of a container query.
         */
        setContainerQueryText(params: Protocol.CSS.SetContainerQueryTextRequest): Promise<Protocol.CSS.SetContainerQueryTextResponse>;

        /**
         * Modifies the expression of a supports at-rule.
         */
        setSupportsText(params: Protocol.CSS.SetSupportsTextRequest): Promise<Protocol.CSS.SetSupportsTextResponse>;

        /**
         * Modifies the expression of a scope at-rule.
         */
        setScopeText(params: Protocol.CSS.SetScopeTextRequest): Promise<Protocol.CSS.SetScopeTextResponse>;

        /**
         * Modifies the rule selector.
         */
        setRuleSelector(params: Protocol.CSS.SetRuleSelectorRequest): Promise<Protocol.CSS.SetRuleSelectorResponse>;

        /**
         * Sets the new stylesheet text.
         */
        setStyleSheetText(params: Protocol.CSS.SetStyleSheetTextRequest): Promise<Protocol.CSS.SetStyleSheetTextResponse>;

        /**
         * Applies specified style edits one after another in the given order.
         */
        setStyleTexts(params: Protocol.CSS.SetStyleTextsRequest): Promise<Protocol.CSS.SetStyleTextsResponse>;

        /**
         * Enables the selector recording.
         */
        startRuleUsageTracking(): Promise<void>;

        /**
         * Stop tracking rule usage and return the list of rules that were used since last call to
         * `takeCoverageDelta` (or since start of coverage instrumentation).
         */
        stopRuleUsageTracking(): Promise<Protocol.CSS.StopRuleUsageTrackingResponse>;

        /**
         * Obtain list of rules that became used since last call to this method (or since start of coverage
         * instrumentation).
         */
        takeCoverageDelta(): Promise<Protocol.CSS.TakeCoverageDeltaResponse>;

        /**
         * Enables/disables rendering of local CSS fonts (enabled by default).
         */
        setLocalFontsEnabled(params: Protocol.CSS.SetLocalFontsEnabledRequest): Promise<void>;

        /**
         * Fires whenever a web font is updated.  A non-empty font parameter indicates a successfully loaded
         * web font.
         */
        on(event: 'fontsUpdated', listener: (params: Protocol.CSS.FontsUpdatedEvent) => void): void;

        /**
         * Fires whenever a MediaQuery result changes (for example, after a browser window has been
         * resized.) The current implementation considers only viewport-dependent media features.
         */
        on(event: 'mediaQueryResultChanged', listener: () => void): void;

        /**
         * Fired whenever an active document stylesheet is added.
         */
        on(event: 'styleSheetAdded', listener: (params: Protocol.CSS.StyleSheetAddedEvent) => void): void;

        /**
         * Fired whenever a stylesheet is changed as a result of the client operation.
         */
        on(event: 'styleSheetChanged', listener: (params: Protocol.CSS.StyleSheetChangedEvent) => void): void;

        /**
         * Fired whenever an active document stylesheet is removed.
         */
        on(event: 'styleSheetRemoved', listener: (params: Protocol.CSS.StyleSheetRemovedEvent) => void): void;

        on(event: 'computedStyleUpdated', listener: (params: Protocol.CSS.ComputedStyleUpdatedEvent) => void): void;

    }

    export interface CacheStorageApi {
        /**
         * Deletes a cache.
         */
        deleteCache(params: Protocol.CacheStorage.DeleteCacheRequest): Promise<void>;

        /**
         * Deletes a cache entry.
         */
        deleteEntry(params: Protocol.CacheStorage.DeleteEntryRequest): Promise<void>;

        /**
         * Requests cache names.
         */
        requestCacheNames(params: Protocol.CacheStorage.RequestCacheNamesRequest): Promise<Protocol.CacheStorage.RequestCacheNamesResponse>;

        /**
         * Fetches cache entry.
         */
        requestCachedResponse(params: Protocol.CacheStorage.RequestCachedResponseRequest): Promise<Protocol.CacheStorage.RequestCachedResponseResponse>;

        /**
         * Requests data from cache.
         */
        requestEntries(params: Protocol.CacheStorage.RequestEntriesRequest): Promise<Protocol.CacheStorage.RequestEntriesResponse>;

    }

    export interface CastApi {
        /**
         * Starts observing for sinks that can be used for tab mirroring, and if set,
         * sinks compatible with |presentationUrl| as well. When sinks are found, a
         * |sinksUpdated| event is fired.
         * Also starts observing for issue messages. When an issue is added or removed,
         * an |issueUpdated| event is fired.
         */
        enable(params: Protocol.Cast.EnableRequest): Promise<void>;

        /**
         * Stops observing for sinks and issues.
         */
        disable(): Promise<void>;

        /**
         * Sets a sink to be used when the web page requests the browser to choose a
         * sink via Presentation API, Remote Playback API, or Cast SDK.
         */
        setSinkToUse(params: Protocol.Cast.SetSinkToUseRequest): Promise<void>;

        /**
         * Starts mirroring the desktop to the sink.
         */
        startDesktopMirroring(params: Protocol.Cast.StartDesktopMirroringRequest): Promise<void>;

        /**
         * Starts mirroring the tab to the sink.
         */
        startTabMirroring(params: Protocol.Cast.StartTabMirroringRequest): Promise<void>;

        /**
         * Stops the active Cast session on the sink.
         */
        stopCasting(params: Protocol.Cast.StopCastingRequest): Promise<void>;

        /**
         * This is fired whenever the list of available sinks changes. A sink is a
         * device or a software surface that you can cast to.
         */
        on(event: 'sinksUpdated', listener: (params: Protocol.Cast.SinksUpdatedEvent) => void): void;

        /**
         * This is fired whenever the outstanding issue/error message changes.
         * |issueMessage| is empty if there is no issue.
         */
        on(event: 'issueUpdated', listener: (params: Protocol.Cast.IssueUpdatedEvent) => void): void;

    }

    export interface DOMApi {
        /**
         * Collects class names for the node with given id and all of it's child nodes.
         */
        collectClassNamesFromSubtree(params: Protocol.DOM.CollectClassNamesFromSubtreeRequest): Promise<Protocol.DOM.CollectClassNamesFromSubtreeResponse>;

        /**
         * Creates a deep copy of the specified node and places it into the target container before the
         * given anchor.
         */
        copyTo(params: Protocol.DOM.CopyToRequest): Promise<Protocol.DOM.CopyToResponse>;

        /**
         * Describes node given its id, does not require domain to be enabled. Does not start tracking any
         * objects, can be used for automation.
         */
        describeNode(params: Protocol.DOM.DescribeNodeRequest): Promise<Protocol.DOM.DescribeNodeResponse>;

        /**
         * Scrolls the specified rect of the given node into view if not already visible.
         * Note: exactly one between nodeId, backendNodeId and objectId should be passed
         * to identify the node.
         */
        scrollIntoViewIfNeeded(params: Protocol.DOM.ScrollIntoViewIfNeededRequest): Promise<void>;

        /**
         * Disables DOM agent for the given page.
         */
        disable(): Promise<void>;

        /**
         * Discards search results from the session with the given id. `getSearchResults` should no longer
         * be called for that search.
         */
        discardSearchResults(params: Protocol.DOM.DiscardSearchResultsRequest): Promise<void>;

        /**
         * Enables DOM agent for the given page.
         */
        enable(params: Protocol.DOM.EnableRequest): Promise<void>;

        /**
         * Focuses the given element.
         */
        focus(params: Protocol.DOM.FocusRequest): Promise<void>;

        /**
         * Returns attributes for the specified node.
         */
        getAttributes(params: Protocol.DOM.GetAttributesRequest): Promise<Protocol.DOM.GetAttributesResponse>;

        /**
         * Returns boxes for the given node.
         */
        getBoxModel(params: Protocol.DOM.GetBoxModelRequest): Promise<Protocol.DOM.GetBoxModelResponse>;

        /**
         * Returns quads that describe node position on the page. This method
         * might return multiple quads for inline nodes.
         */
        getContentQuads(params: Protocol.DOM.GetContentQuadsRequest): Promise<Protocol.DOM.GetContentQuadsResponse>;

        /**
         * Returns the root DOM node (and optionally the subtree) to the caller.
         * Implicitly enables the DOM domain events for the current target.
         */
        getDocument(params: Protocol.DOM.GetDocumentRequest): Promise<Protocol.DOM.GetDocumentResponse>;

        /**
         * Returns the root DOM node (and optionally the subtree) to the caller.
         * Deprecated, as it is not designed to work well with the rest of the DOM agent.
         * Use DOMSnapshot.captureSnapshot instead.
         */
        getFlattenedDocument(params: Protocol.DOM.GetFlattenedDocumentRequest): Promise<Protocol.DOM.GetFlattenedDocumentResponse>;

        /**
         * Finds nodes with a given computed style in a subtree.
         */
        getNodesForSubtreeByStyle(params: Protocol.DOM.GetNodesForSubtreeByStyleRequest): Promise<Protocol.DOM.GetNodesForSubtreeByStyleResponse>;

        /**
         * Returns node id at given location. Depending on whether DOM domain is enabled, nodeId is
         * either returned or not.
         */
        getNodeForLocation(params: Protocol.DOM.GetNodeForLocationRequest): Promise<Protocol.DOM.GetNodeForLocationResponse>;

        /**
         * Returns node's HTML markup.
         */
        getOuterHTML(params: Protocol.DOM.GetOuterHTMLRequest): Promise<Protocol.DOM.GetOuterHTMLResponse>;

        /**
         * Returns the id of the nearest ancestor that is a relayout boundary.
         */
        getRelayoutBoundary(params: Protocol.DOM.GetRelayoutBoundaryRequest): Promise<Protocol.DOM.GetRelayoutBoundaryResponse>;

        /**
         * Returns search results from given `fromIndex` to given `toIndex` from the search with the given
         * identifier.
         */
        getSearchResults(params: Protocol.DOM.GetSearchResultsRequest): Promise<Protocol.DOM.GetSearchResultsResponse>;

        /**
         * Hides any highlight.
         */
        hideHighlight(): Promise<void>;

        /**
         * Highlights DOM node.
         */
        highlightNode(): Promise<void>;

        /**
         * Highlights given rectangle.
         */
        highlightRect(): Promise<void>;

        /**
         * Marks last undoable state.
         */
        markUndoableState(): Promise<void>;

        /**
         * Moves node into the new container, places it before the given anchor.
         */
        moveTo(params: Protocol.DOM.MoveToRequest): Promise<Protocol.DOM.MoveToResponse>;

        /**
         * Searches for a given string in the DOM tree. Use `getSearchResults` to access search results or
         * `cancelSearch` to end this search session.
         */
        performSearch(params: Protocol.DOM.PerformSearchRequest): Promise<Protocol.DOM.PerformSearchResponse>;

        /**
         * Requests that the node is sent to the caller given its path. // FIXME, use XPath
         */
        pushNodeByPathToFrontend(params: Protocol.DOM.PushNodeByPathToFrontendRequest): Promise<Protocol.DOM.PushNodeByPathToFrontendResponse>;

        /**
         * Requests that a batch of nodes is sent to the caller given their backend node ids.
         */
        pushNodesByBackendIdsToFrontend(params: Protocol.DOM.PushNodesByBackendIdsToFrontendRequest): Promise<Protocol.DOM.PushNodesByBackendIdsToFrontendResponse>;

        /**
         * Executes `querySelector` on a given node.
         */
        querySelector(params: Protocol.DOM.QuerySelectorRequest): Promise<Protocol.DOM.QuerySelectorResponse>;

        /**
         * Executes `querySelectorAll` on a given node.
         */
        querySelectorAll(params: Protocol.DOM.QuerySelectorAllRequest): Promise<Protocol.DOM.QuerySelectorAllResponse>;

        /**
         * Returns NodeIds of current top layer elements.
         * Top layer is rendered closest to the user within a viewport, therefore its elements always
         * appear on top of all other content.
         */
        getTopLayerElements(): Promise<Protocol.DOM.GetTopLayerElementsResponse>;

        /**
         * Returns the NodeId of the matched element according to certain relations.
         */
        getElementByRelation(params: Protocol.DOM.GetElementByRelationRequest): Promise<Protocol.DOM.GetElementByRelationResponse>;

        /**
         * Re-does the last undone action.
         */
        redo(): Promise<void>;

        /**
         * Removes attribute with given name from an element with given id.
         */
        removeAttribute(params: Protocol.DOM.RemoveAttributeRequest): Promise<void>;

        /**
         * Removes node with given id.
         */
        removeNode(params: Protocol.DOM.RemoveNodeRequest): Promise<void>;

        /**
         * Requests that children of the node with given id are returned to the caller in form of
         * `setChildNodes` events where not only immediate children are retrieved, but all children down to
         * the specified depth.
         */
        requestChildNodes(params: Protocol.DOM.RequestChildNodesRequest): Promise<void>;

        /**
         * Requests that the node is sent to the caller given the JavaScript node object reference. All
         * nodes that form the path from the node to the root are also sent to the client as a series of
         * `setChildNodes` notifications.
         */
        requestNode(params: Protocol.DOM.RequestNodeRequest): Promise<Protocol.DOM.RequestNodeResponse>;

        /**
         * Resolves the JavaScript node object for a given NodeId or BackendNodeId.
         */
        resolveNode(params: Protocol.DOM.ResolveNodeRequest): Promise<Protocol.DOM.ResolveNodeResponse>;

        /**
         * Sets attribute for an element with given id.
         */
        setAttributeValue(params: Protocol.DOM.SetAttributeValueRequest): Promise<void>;

        /**
         * Sets attributes on element with given id. This method is useful when user edits some existing
         * attribute value and types in several attribute name/value pairs.
         */
        setAttributesAsText(params: Protocol.DOM.SetAttributesAsTextRequest): Promise<void>;

        /**
         * Sets files for the given file input element.
         */
        setFileInputFiles(params: Protocol.DOM.SetFileInputFilesRequest): Promise<void>;

        /**
         * Sets if stack traces should be captured for Nodes. See `Node.getNodeStackTraces`. Default is disabled.
         */
        setNodeStackTracesEnabled(params: Protocol.DOM.SetNodeStackTracesEnabledRequest): Promise<void>;

        /**
         * Gets stack traces associated with a Node. As of now, only provides stack trace for Node creation.
         */
        getNodeStackTraces(params: Protocol.DOM.GetNodeStackTracesRequest): Promise<Protocol.DOM.GetNodeStackTracesResponse>;

        /**
         * Returns file information for the given
         * File wrapper.
         */
        getFileInfo(params: Protocol.DOM.GetFileInfoRequest): Promise<Protocol.DOM.GetFileInfoResponse>;

        /**
         * Returns list of detached nodes
         */
        getDetachedDomNodes(): Promise<Protocol.DOM.GetDetachedDomNodesResponse>;

        /**
         * Enables console to refer to the node with given id via $x (see Command Line API for more details
         * $x functions).
         */
        setInspectedNode(params: Protocol.DOM.SetInspectedNodeRequest): Promise<void>;

        /**
         * Sets node name for a node with given id.
         */
        setNodeName(params: Protocol.DOM.SetNodeNameRequest): Promise<Protocol.DOM.SetNodeNameResponse>;

        /**
         * Sets node value for a node with given id.
         */
        setNodeValue(params: Protocol.DOM.SetNodeValueRequest): Promise<void>;

        /**
         * Sets node HTML markup, returns new node id.
         */
        setOuterHTML(params: Protocol.DOM.SetOuterHTMLRequest): Promise<void>;

        /**
         * Undoes the last performed action.
         */
        undo(): Promise<void>;

        /**
         * Returns iframe node that owns iframe with the given domain.
         */
        getFrameOwner(params: Protocol.DOM.GetFrameOwnerRequest): Promise<Protocol.DOM.GetFrameOwnerResponse>;

        /**
         * Returns the query container of the given node based on container query
         * conditions: containerName, physical and logical axes, and whether it queries
         * scroll-state. If no axes are provided and queriesScrollState is false, the
         * style container is returned, which is the direct parent or the closest
         * element with a matching container-name.
         */
        getContainerForNode(params: Protocol.DOM.GetContainerForNodeRequest): Promise<Protocol.DOM.GetContainerForNodeResponse>;

        /**
         * Returns the descendants of a container query container that have
         * container queries against this container.
         */
        getQueryingDescendantsForContainer(params: Protocol.DOM.GetQueryingDescendantsForContainerRequest): Promise<Protocol.DOM.GetQueryingDescendantsForContainerResponse>;

        /**
         * Returns the target anchor element of the given anchor query according to
         * https://www.w3.org/TR/css-anchor-position-1/#target.
         */
        getAnchorElement(params: Protocol.DOM.GetAnchorElementRequest): Promise<Protocol.DOM.GetAnchorElementResponse>;

        /**
         * Fired when `Element`'s attribute is modified.
         */
        on(event: 'attributeModified', listener: (params: Protocol.DOM.AttributeModifiedEvent) => void): void;

        /**
         * Fired when `Element`'s attribute is removed.
         */
        on(event: 'attributeRemoved', listener: (params: Protocol.DOM.AttributeRemovedEvent) => void): void;

        /**
         * Mirrors `DOMCharacterDataModified` event.
         */
        on(event: 'characterDataModified', listener: (params: Protocol.DOM.CharacterDataModifiedEvent) => void): void;

        /**
         * Fired when `Container`'s child node count has changed.
         */
        on(event: 'childNodeCountUpdated', listener: (params: Protocol.DOM.ChildNodeCountUpdatedEvent) => void): void;

        /**
         * Mirrors `DOMNodeInserted` event.
         */
        on(event: 'childNodeInserted', listener: (params: Protocol.DOM.ChildNodeInsertedEvent) => void): void;

        /**
         * Mirrors `DOMNodeRemoved` event.
         */
        on(event: 'childNodeRemoved', listener: (params: Protocol.DOM.ChildNodeRemovedEvent) => void): void;

        /**
         * Called when distribution is changed.
         */
        on(event: 'distributedNodesUpdated', listener: (params: Protocol.DOM.DistributedNodesUpdatedEvent) => void): void;

        /**
         * Fired when `Document` has been totally updated. Node ids are no longer valid.
         */
        on(event: 'documentUpdated', listener: () => void): void;

        /**
         * Fired when `Element`'s inline style is modified via a CSS property modification.
         */
        on(event: 'inlineStyleInvalidated', listener: (params: Protocol.DOM.InlineStyleInvalidatedEvent) => void): void;

        /**
         * Called when a pseudo element is added to an element.
         */
        on(event: 'pseudoElementAdded', listener: (params: Protocol.DOM.PseudoElementAddedEvent) => void): void;

        /**
         * Called when top layer elements are changed.
         */
        on(event: 'topLayerElementsUpdated', listener: () => void): void;

        /**
         * Fired when a node's scrollability state changes.
         */
        on(event: 'scrollableFlagUpdated', listener: (params: Protocol.DOM.ScrollableFlagUpdatedEvent) => void): void;

        /**
         * Called when a pseudo element is removed from an element.
         */
        on(event: 'pseudoElementRemoved', listener: (params: Protocol.DOM.PseudoElementRemovedEvent) => void): void;

        /**
         * Fired when backend wants to provide client with the missing DOM structure. This happens upon
         * most of the calls requesting node ids.
         */
        on(event: 'setChildNodes', listener: (params: Protocol.DOM.SetChildNodesEvent) => void): void;

        /**
         * Called when shadow root is popped from the element.
         */
        on(event: 'shadowRootPopped', listener: (params: Protocol.DOM.ShadowRootPoppedEvent) => void): void;

        /**
         * Called when shadow root is pushed into the element.
         */
        on(event: 'shadowRootPushed', listener: (params: Protocol.DOM.ShadowRootPushedEvent) => void): void;

    }

    export interface DOMDebuggerApi {
        /**
         * Returns event listeners of the given object.
         */
        getEventListeners(params: Protocol.DOMDebugger.GetEventListenersRequest): Promise<Protocol.DOMDebugger.GetEventListenersResponse>;

        /**
         * Removes DOM breakpoint that was set using `setDOMBreakpoint`.
         */
        removeDOMBreakpoint(params: Protocol.DOMDebugger.RemoveDOMBreakpointRequest): Promise<void>;

        /**
         * Removes breakpoint on particular DOM event.
         */
        removeEventListenerBreakpoint(params: Protocol.DOMDebugger.RemoveEventListenerBreakpointRequest): Promise<void>;

        /**
         * Removes breakpoint on particular native event.
         */
        removeInstrumentationBreakpoint(params: Protocol.DOMDebugger.RemoveInstrumentationBreakpointRequest): Promise<void>;

        /**
         * Removes breakpoint from XMLHttpRequest.
         */
        removeXHRBreakpoint(params: Protocol.DOMDebugger.RemoveXHRBreakpointRequest): Promise<void>;

        /**
         * Sets breakpoint on particular CSP violations.
         */
        setBreakOnCSPViolation(params: Protocol.DOMDebugger.SetBreakOnCSPViolationRequest): Promise<void>;

        /**
         * Sets breakpoint on particular operation with DOM.
         */
        setDOMBreakpoint(params: Protocol.DOMDebugger.SetDOMBreakpointRequest): Promise<void>;

        /**
         * Sets breakpoint on particular DOM event.
         */
        setEventListenerBreakpoint(params: Protocol.DOMDebugger.SetEventListenerBreakpointRequest): Promise<void>;

        /**
         * Sets breakpoint on particular native event.
         */
        setInstrumentationBreakpoint(params: Protocol.DOMDebugger.SetInstrumentationBreakpointRequest): Promise<void>;

        /**
         * Sets breakpoint on XMLHttpRequest.
         */
        setXHRBreakpoint(params: Protocol.DOMDebugger.SetXHRBreakpointRequest): Promise<void>;

    }

    export interface EventBreakpointsApi {
        /**
         * Sets breakpoint on particular native event.
         */
        setInstrumentationBreakpoint(params: Protocol.EventBreakpoints.SetInstrumentationBreakpointRequest): Promise<void>;

        /**
         * Removes breakpoint on particular native event.
         */
        removeInstrumentationBreakpoint(params: Protocol.EventBreakpoints.RemoveInstrumentationBreakpointRequest): Promise<void>;

        /**
         * Removes all breakpoints
         */
        disable(): Promise<void>;

    }

    export interface DOMSnapshotApi {
        /**
         * Disables DOM snapshot agent for the given page.
         */
        disable(): Promise<void>;

        /**
         * Enables DOM snapshot agent for the given page.
         */
        enable(): Promise<void>;

        /**
         * Returns a document snapshot, including the full DOM tree of the root node (including iframes,
         * template contents, and imported documents) in a flattened array, as well as layout and
         * white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
         * flattened.
         */
        getSnapshot(params: Protocol.DOMSnapshot.GetSnapshotRequest): Promise<Protocol.DOMSnapshot.GetSnapshotResponse>;

        /**
         * Returns a document snapshot, including the full DOM tree of the root node (including iframes,
         * template contents, and imported documents) in a flattened array, as well as layout and
         * white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
         * flattened.
         */
        captureSnapshot(params: Protocol.DOMSnapshot.CaptureSnapshotRequest): Promise<Protocol.DOMSnapshot.CaptureSnapshotResponse>;

    }

    export interface DOMStorageApi {
        clear(params: Protocol.DOMStorage.ClearRequest): Promise<void>;

        /**
         * Disables storage tracking, prevents storage events from being sent to the client.
         */
        disable(): Promise<void>;

        /**
         * Enables storage tracking, storage events will now be delivered to the client.
         */
        enable(): Promise<void>;

        getDOMStorageItems(params: Protocol.DOMStorage.GetDOMStorageItemsRequest): Promise<Protocol.DOMStorage.GetDOMStorageItemsResponse>;

        removeDOMStorageItem(params: Protocol.DOMStorage.RemoveDOMStorageItemRequest): Promise<void>;

        setDOMStorageItem(params: Protocol.DOMStorage.SetDOMStorageItemRequest): Promise<void>;

        on(event: 'domStorageItemAdded', listener: (params: Protocol.DOMStorage.DomStorageItemAddedEvent) => void): void;

        on(event: 'domStorageItemRemoved', listener: (params: Protocol.DOMStorage.DomStorageItemRemovedEvent) => void): void;

        on(event: 'domStorageItemUpdated', listener: (params: Protocol.DOMStorage.DomStorageItemUpdatedEvent) => void): void;

        on(event: 'domStorageItemsCleared', listener: (params: Protocol.DOMStorage.DomStorageItemsClearedEvent) => void): void;

    }

    export interface DeviceOrientationApi {
        /**
         * Clears the overridden Device Orientation.
         */
        clearDeviceOrientationOverride(): Promise<void>;

        /**
         * Overrides the Device Orientation.
         */
        setDeviceOrientationOverride(params: Protocol.DeviceOrientation.SetDeviceOrientationOverrideRequest): Promise<void>;

    }

    export interface EmulationApi {
        /**
         * Tells whether emulation is supported.
         */
        canEmulate(): Promise<Protocol.Emulation.CanEmulateResponse>;

        /**
         * Clears the overridden device metrics.
         */
        clearDeviceMetricsOverride(): Promise<void>;

        /**
         * Clears the overridden Geolocation Position and Error.
         */
        clearGeolocationOverride(): Promise<void>;

        /**
         * Requests that page scale factor is reset to initial values.
         */
        resetPageScaleFactor(): Promise<void>;

        /**
         * Enables or disables simulating a focused and active page.
         */
        setFocusEmulationEnabled(params: Protocol.Emulation.SetFocusEmulationEnabledRequest): Promise<void>;

        /**
         * Automatically render all web contents using a dark theme.
         */
        setAutoDarkModeOverride(params: Protocol.Emulation.SetAutoDarkModeOverrideRequest): Promise<void>;

        /**
         * Enables CPU throttling to emulate slow CPUs.
         */
        setCPUThrottlingRate(params: Protocol.Emulation.SetCPUThrottlingRateRequest): Promise<void>;

        /**
         * Sets or clears an override of the default background color of the frame. This override is used
         * if the content does not specify one.
         */
        setDefaultBackgroundColorOverride(params: Protocol.Emulation.SetDefaultBackgroundColorOverrideRequest): Promise<void>;

        /**
         * Overrides the values for env(safe-area-inset-*) and env(safe-area-max-inset-*). Unset values will cause the
         * respective variables to be undefined, even if previously overridden.
         */
        setSafeAreaInsetsOverride(params: Protocol.Emulation.SetSafeAreaInsetsOverrideRequest): Promise<void>;

        /**
         * Overrides the values of device screen dimensions (window.screen.width, window.screen.height,
         * window.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media
         * query results).
         */
        setDeviceMetricsOverride(params: Protocol.Emulation.SetDeviceMetricsOverrideRequest): Promise<void>;

        /**
         * Start reporting the given posture value to the Device Posture API.
         * This override can also be set in setDeviceMetricsOverride().
         */
        setDevicePostureOverride(params: Protocol.Emulation.SetDevicePostureOverrideRequest): Promise<void>;

        /**
         * Clears a device posture override set with either setDeviceMetricsOverride()
         * or setDevicePostureOverride() and starts using posture information from the
         * platform again.
         * Does nothing if no override is set.
         */
        clearDevicePostureOverride(): Promise<void>;

        /**
         * Start using the given display features to pupulate the Viewport Segments API.
         * This override can also be set in setDeviceMetricsOverride().
         */
        setDisplayFeaturesOverride(params: Protocol.Emulation.SetDisplayFeaturesOverrideRequest): Promise<void>;

        /**
         * Clears the display features override set with either setDeviceMetricsOverride()
         * or setDisplayFeaturesOverride() and starts using display features from the
         * platform again.
         * Does nothing if no override is set.
         */
        clearDisplayFeaturesOverride(): Promise<void>;

        setScrollbarsHidden(params: Protocol.Emulation.SetScrollbarsHiddenRequest): Promise<void>;

        setDocumentCookieDisabled(params: Protocol.Emulation.SetDocumentCookieDisabledRequest): Promise<void>;

        setEmitTouchEventsForMouse(params: Protocol.Emulation.SetEmitTouchEventsForMouseRequest): Promise<void>;

        /**
         * Emulates the given media type or media feature for CSS media queries.
         */
        setEmulatedMedia(params: Protocol.Emulation.SetEmulatedMediaRequest): Promise<void>;

        /**
         * Emulates the given vision deficiency.
         */
        setEmulatedVisionDeficiency(params: Protocol.Emulation.SetEmulatedVisionDeficiencyRequest): Promise<void>;

        /**
         * Overrides the Geolocation Position or Error. Omitting latitude, longitude or
         * accuracy emulates position unavailable.
         */
        setGeolocationOverride(params: Protocol.Emulation.SetGeolocationOverrideRequest): Promise<void>;

        getOverriddenSensorInformation(params: Protocol.Emulation.GetOverriddenSensorInformationRequest): Promise<Protocol.Emulation.GetOverriddenSensorInformationResponse>;

        /**
         * Overrides a platform sensor of a given type. If |enabled| is true, calls to
         * Sensor.start() will use a virtual sensor as backend rather than fetching
         * data from a real hardware sensor. Otherwise, existing virtual
         * sensor-backend Sensor objects will fire an error event and new calls to
         * Sensor.start() will attempt to use a real sensor instead.
         */
        setSensorOverrideEnabled(params: Protocol.Emulation.SetSensorOverrideEnabledRequest): Promise<void>;

        /**
         * Updates the sensor readings reported by a sensor type previously overridden
         * by setSensorOverrideEnabled.
         */
        setSensorOverrideReadings(params: Protocol.Emulation.SetSensorOverrideReadingsRequest): Promise<void>;

        /**
         * Overrides a pressure source of a given type, as used by the Compute
         * Pressure API, so that updates to PressureObserver.observe() are provided
         * via setPressureStateOverride instead of being retrieved from
         * platform-provided telemetry data.
         */
        setPressureSourceOverrideEnabled(params: Protocol.Emulation.SetPressureSourceOverrideEnabledRequest): Promise<void>;

        /**
         * TODO: OBSOLETE: To remove when setPressureDataOverride is merged.
         * Provides a given pressure state that will be processed and eventually be
         * delivered to PressureObserver users. |source| must have been previously
         * overridden by setPressureSourceOverrideEnabled.
         */
        setPressureStateOverride(params: Protocol.Emulation.SetPressureStateOverrideRequest): Promise<void>;

        /**
         * Provides a given pressure data set that will be processed and eventually be
         * delivered to PressureObserver users. |source| must have been previously
         * overridden by setPressureSourceOverrideEnabled.
         */
        setPressureDataOverride(params: Protocol.Emulation.SetPressureDataOverrideRequest): Promise<void>;

        /**
         * Overrides the Idle state.
         */
        setIdleOverride(params: Protocol.Emulation.SetIdleOverrideRequest): Promise<void>;

        /**
         * Clears Idle state overrides.
         */
        clearIdleOverride(): Promise<void>;

        /**
         * Overrides value returned by the javascript navigator object.
         */
        setNavigatorOverrides(params: Protocol.Emulation.SetNavigatorOverridesRequest): Promise<void>;

        /**
         * Sets a specified page scale factor.
         */
        setPageScaleFactor(params: Protocol.Emulation.SetPageScaleFactorRequest): Promise<void>;

        /**
         * Switches script execution in the page.
         */
        setScriptExecutionDisabled(params: Protocol.Emulation.SetScriptExecutionDisabledRequest): Promise<void>;

        /**
         * Enables touch on platforms which do not support them.
         */
        setTouchEmulationEnabled(params: Protocol.Emulation.SetTouchEmulationEnabledRequest): Promise<void>;

        /**
         * Turns on virtual time for all frames (replacing real-time with a synthetic time source) and sets
         * the current virtual time policy.  Note this supersedes any previous time budget.
         */
        setVirtualTimePolicy(params: Protocol.Emulation.SetVirtualTimePolicyRequest): Promise<Protocol.Emulation.SetVirtualTimePolicyResponse>;

        /**
         * Overrides default host system locale with the specified one.
         */
        setLocaleOverride(params: Protocol.Emulation.SetLocaleOverrideRequest): Promise<void>;

        /**
         * Overrides default host system timezone with the specified one.
         */
        setTimezoneOverride(params: Protocol.Emulation.SetTimezoneOverrideRequest): Promise<void>;

        /**
         * Resizes the frame/viewport of the page. Note that this does not affect the frame's container
         * (e.g. browser window). Can be used to produce screenshots of the specified size. Not supported
         * on Android.
         */
        setVisibleSize(params: Protocol.Emulation.SetVisibleSizeRequest): Promise<void>;

        setDisabledImageTypes(params: Protocol.Emulation.SetDisabledImageTypesRequest): Promise<void>;

        setHardwareConcurrencyOverride(params: Protocol.Emulation.SetHardwareConcurrencyOverrideRequest): Promise<void>;

        /**
         * Allows overriding user agent with the given string.
         * `userAgentMetadata` must be set for Client Hint headers to be sent.
         */
        setUserAgentOverride(params: Protocol.Emulation.SetUserAgentOverrideRequest): Promise<void>;

        /**
         * Allows overriding the automation flag.
         */
        setAutomationOverride(params: Protocol.Emulation.SetAutomationOverrideRequest): Promise<void>;

        /**
         * Allows overriding the difference between the small and large viewport sizes, which determine the
         * value of the `svh` and `lvh` unit, respectively. Only supported for top-level frames.
         */
        setSmallViewportHeightDifferenceOverride(params: Protocol.Emulation.SetSmallViewportHeightDifferenceOverrideRequest): Promise<void>;

        /**
         * Notification sent after the virtual time budget for the current VirtualTimePolicy has run out.
         */
        on(event: 'virtualTimeBudgetExpired', listener: () => void): void;

    }

    export interface HeadlessExperimentalApi {
        /**
         * Sends a BeginFrame to the target and returns when the frame was completed. Optionally captures a
         * screenshot from the resulting frame. Requires that the target was created with enabled
         * BeginFrameControl. Designed for use with --run-all-compositor-stages-before-draw, see also
         * https://goo.gle/chrome-headless-rendering for more background.
         */
        beginFrame(params: Protocol.HeadlessExperimental.BeginFrameRequest): Promise<Protocol.HeadlessExperimental.BeginFrameResponse>;

        /**
         * Disables headless events for the target.
         */
        disable(): Promise<void>;

        /**
         * Enables headless events for the target.
         */
        enable(): Promise<void>;

    }

    export interface IOApi {
        /**
         * Close the stream, discard any temporary backing storage.
         */
        close(params: Protocol.IO.CloseRequest): Promise<void>;

        /**
         * Read a chunk of the stream
         */
        read(params: Protocol.IO.ReadRequest): Promise<Protocol.IO.ReadResponse>;

        /**
         * Return UUID of Blob object specified by a remote object id.
         */
        resolveBlob(params: Protocol.IO.ResolveBlobRequest): Promise<Protocol.IO.ResolveBlobResponse>;

    }

    export interface FileSystemApi {
        getDirectory(params: Protocol.FileSystem.GetDirectoryRequest): Promise<Protocol.FileSystem.GetDirectoryResponse>;

    }

    export interface IndexedDBApi {
        /**
         * Clears all entries from an object store.
         */
        clearObjectStore(params: Protocol.IndexedDB.ClearObjectStoreRequest): Promise<void>;

        /**
         * Deletes a database.
         */
        deleteDatabase(params: Protocol.IndexedDB.DeleteDatabaseRequest): Promise<void>;

        /**
         * Delete a range of entries from an object store
         */
        deleteObjectStoreEntries(params: Protocol.IndexedDB.DeleteObjectStoreEntriesRequest): Promise<void>;

        /**
         * Disables events from backend.
         */
        disable(): Promise<void>;

        /**
         * Enables events from backend.
         */
        enable(): Promise<void>;

        /**
         * Requests data from object store or index.
         */
        requestData(params: Protocol.IndexedDB.RequestDataRequest): Promise<Protocol.IndexedDB.RequestDataResponse>;

        /**
         * Gets metadata of an object store.
         */
        getMetadata(params: Protocol.IndexedDB.GetMetadataRequest): Promise<Protocol.IndexedDB.GetMetadataResponse>;

        /**
         * Requests database with given name in given frame.
         */
        requestDatabase(params: Protocol.IndexedDB.RequestDatabaseRequest): Promise<Protocol.IndexedDB.RequestDatabaseResponse>;

        /**
         * Requests database names for given security origin.
         */
        requestDatabaseNames(params: Protocol.IndexedDB.RequestDatabaseNamesRequest): Promise<Protocol.IndexedDB.RequestDatabaseNamesResponse>;

    }

    export interface InputApi {
        /**
         * Dispatches a drag event into the page.
         */
        dispatchDragEvent(params: Protocol.Input.DispatchDragEventRequest): Promise<void>;

        /**
         * Dispatches a key event to the page.
         */
        dispatchKeyEvent(params: Protocol.Input.DispatchKeyEventRequest): Promise<void>;

        /**
         * This method emulates inserting text that doesn't come from a key press,
         * for example an emoji keyboard or an IME.
         */
        insertText(params: Protocol.Input.InsertTextRequest): Promise<void>;

        /**
         * This method sets the current candidate text for IME.
         * Use imeCommitComposition to commit the final text.
         * Use imeSetComposition with empty string as text to cancel composition.
         */
        imeSetComposition(params: Protocol.Input.ImeSetCompositionRequest): Promise<void>;

        /**
         * Dispatches a mouse event to the page.
         */
        dispatchMouseEvent(params: Protocol.Input.DispatchMouseEventRequest): Promise<void>;

        /**
         * Dispatches a touch event to the page.
         */
        dispatchTouchEvent(params: Protocol.Input.DispatchTouchEventRequest): Promise<void>;

        /**
         * Cancels any active dragging in the page.
         */
        cancelDragging(): Promise<void>;

        /**
         * Emulates touch event from the mouse event parameters.
         */
        emulateTouchFromMouseEvent(params: Protocol.Input.EmulateTouchFromMouseEventRequest): Promise<void>;

        /**
         * Ignores input events (useful while auditing page).
         */
        setIgnoreInputEvents(params: Protocol.Input.SetIgnoreInputEventsRequest): Promise<void>;

        /**
         * Prevents default drag and drop behavior and instead emits `Input.dragIntercepted` events.
         * Drag and drop behavior can be directly controlled via `Input.dispatchDragEvent`.
         */
        setInterceptDrags(params: Protocol.Input.SetInterceptDragsRequest): Promise<void>;

        /**
         * Synthesizes a pinch gesture over a time period by issuing appropriate touch events.
         */
        synthesizePinchGesture(params: Protocol.Input.SynthesizePinchGestureRequest): Promise<void>;

        /**
         * Synthesizes a scroll gesture over a time period by issuing appropriate touch events.
         */
        synthesizeScrollGesture(params: Protocol.Input.SynthesizeScrollGestureRequest): Promise<void>;

        /**
         * Synthesizes a tap gesture over a time period by issuing appropriate touch events.
         */
        synthesizeTapGesture(params: Protocol.Input.SynthesizeTapGestureRequest): Promise<void>;

        /**
         * Emitted only when `Input.setInterceptDrags` is enabled. Use this data with `Input.dispatchDragEvent` to
         * restore normal drag and drop behavior.
         */
        on(event: 'dragIntercepted', listener: (params: Protocol.Input.DragInterceptedEvent) => void): void;

    }

    export interface InspectorApi {
        /**
         * Disables inspector domain notifications.
         */
        disable(): Promise<void>;

        /**
         * Enables inspector domain notifications.
         */
        enable(): Promise<void>;

        /**
         * Fired when remote debugging connection is about to be terminated. Contains detach reason.
         */
        on(event: 'detached', listener: (params: Protocol.Inspector.DetachedEvent) => void): void;

        /**
         * Fired when debugging target has crashed
         */
        on(event: 'targetCrashed', listener: () => void): void;

        /**
         * Fired when debugging target has reloaded after crash
         */
        on(event: 'targetReloadedAfterCrash', listener: () => void): void;

    }

    export interface LayerTreeApi {
        /**
         * Provides the reasons why the given layer was composited.
         */
        compositingReasons(params: Protocol.LayerTree.CompositingReasonsRequest): Promise<Protocol.LayerTree.CompositingReasonsResponse>;

        /**
         * Disables compositing tree inspection.
         */
        disable(): Promise<void>;

        /**
         * Enables compositing tree inspection.
         */
        enable(): Promise<void>;

        /**
         * Returns the snapshot identifier.
         */
        loadSnapshot(params: Protocol.LayerTree.LoadSnapshotRequest): Promise<Protocol.LayerTree.LoadSnapshotResponse>;

        /**
         * Returns the layer snapshot identifier.
         */
        makeSnapshot(params: Protocol.LayerTree.MakeSnapshotRequest): Promise<Protocol.LayerTree.MakeSnapshotResponse>;

        profileSnapshot(params: Protocol.LayerTree.ProfileSnapshotRequest): Promise<Protocol.LayerTree.ProfileSnapshotResponse>;

        /**
         * Releases layer snapshot captured by the back-end.
         */
        releaseSnapshot(params: Protocol.LayerTree.ReleaseSnapshotRequest): Promise<void>;

        /**
         * Replays the layer snapshot and returns the resulting bitmap.
         */
        replaySnapshot(params: Protocol.LayerTree.ReplaySnapshotRequest): Promise<Protocol.LayerTree.ReplaySnapshotResponse>;

        /**
         * Replays the layer snapshot and returns canvas log.
         */
        snapshotCommandLog(params: Protocol.LayerTree.SnapshotCommandLogRequest): Promise<Protocol.LayerTree.SnapshotCommandLogResponse>;

        on(event: 'layerPainted', listener: (params: Protocol.LayerTree.LayerPaintedEvent) => void): void;

        on(event: 'layerTreeDidChange', listener: (params: Protocol.LayerTree.LayerTreeDidChangeEvent) => void): void;

    }

    export interface LogApi {
        /**
         * Clears the log.
         */
        clear(): Promise<void>;

        /**
         * Disables log domain, prevents further log entries from being reported to the client.
         */
        disable(): Promise<void>;

        /**
         * Enables log domain, sends the entries collected so far to the client by means of the
         * `entryAdded` notification.
         */
        enable(): Promise<void>;

        /**
         * start violation reporting.
         */
        startViolationsReport(params: Protocol.Log.StartViolationsReportRequest): Promise<void>;

        /**
         * Stop violation reporting.
         */
        stopViolationsReport(): Promise<void>;

        /**
         * Issued when new message was logged.
         */
        on(event: 'entryAdded', listener: (params: Protocol.Log.EntryAddedEvent) => void): void;

    }

    export interface MemoryApi {
        /**
         * Retruns current DOM object counters.
         */
        getDOMCounters(): Promise<Protocol.Memory.GetDOMCountersResponse>;

        /**
         * Retruns DOM object counters after preparing renderer for leak detection.
         */
        getDOMCountersForLeakDetection(): Promise<Protocol.Memory.GetDOMCountersForLeakDetectionResponse>;

        /**
         * Prepares for leak detection by terminating workers, stopping spellcheckers,
         * dropping non-essential internal caches, running garbage collections, etc.
         */
        prepareForLeakDetection(): Promise<void>;

        /**
         * Simulate OomIntervention by purging V8 memory.
         */
        forciblyPurgeJavaScriptMemory(): Promise<void>;

        /**
         * Enable/disable suppressing memory pressure notifications in all processes.
         */
        setPressureNotificationsSuppressed(params: Protocol.Memory.SetPressureNotificationsSuppressedRequest): Promise<void>;

        /**
         * Simulate a memory pressure notification in all processes.
         */
        simulatePressureNotification(params: Protocol.Memory.SimulatePressureNotificationRequest): Promise<void>;

        /**
         * Start collecting native memory profile.
         */
        startSampling(params: Protocol.Memory.StartSamplingRequest): Promise<void>;

        /**
         * Stop collecting native memory profile.
         */
        stopSampling(): Promise<void>;

        /**
         * Retrieve native memory allocations profile
         * collected since renderer process startup.
         */
        getAllTimeSamplingProfile(): Promise<Protocol.Memory.GetAllTimeSamplingProfileResponse>;

        /**
         * Retrieve native memory allocations profile
         * collected since browser process startup.
         */
        getBrowserSamplingProfile(): Promise<Protocol.Memory.GetBrowserSamplingProfileResponse>;

        /**
         * Retrieve native memory allocations profile collected since last
         * `startSampling` call.
         */
        getSamplingProfile(): Promise<Protocol.Memory.GetSamplingProfileResponse>;

    }

    export interface NetworkApi {
        /**
         * Sets a list of content encodings that will be accepted. Empty list means no encoding is accepted.
         */
        setAcceptedEncodings(params: Protocol.Network.SetAcceptedEncodingsRequest): Promise<void>;

        /**
         * Clears accepted encodings set by setAcceptedEncodings
         */
        clearAcceptedEncodingsOverride(): Promise<void>;

        /**
         * Tells whether clearing browser cache is supported.
         */
        canClearBrowserCache(): Promise<Protocol.Network.CanClearBrowserCacheResponse>;

        /**
         * Tells whether clearing browser cookies is supported.
         */
        canClearBrowserCookies(): Promise<Protocol.Network.CanClearBrowserCookiesResponse>;

        /**
         * Tells whether emulation of network conditions is supported.
         */
        canEmulateNetworkConditions(): Promise<Protocol.Network.CanEmulateNetworkConditionsResponse>;

        /**
         * Clears browser cache.
         */
        clearBrowserCache(): Promise<void>;

        /**
         * Clears browser cookies.
         */
        clearBrowserCookies(): Promise<void>;

        /**
         * Response to Network.requestIntercepted which either modifies the request to continue with any
         * modifications, or blocks it, or completes it with the provided response bytes. If a network
         * fetch occurs as a result which encounters a redirect an additional Network.requestIntercepted
         * event will be sent with the same InterceptionId.
         * Deprecated, use Fetch.continueRequest, Fetch.fulfillRequest and Fetch.failRequest instead.
         */
        continueInterceptedRequest(params: Protocol.Network.ContinueInterceptedRequestRequest): Promise<void>;

        /**
         * Deletes browser cookies with matching name and url or domain/path/partitionKey pair.
         */
        deleteCookies(params: Protocol.Network.DeleteCookiesRequest): Promise<void>;

        /**
         * Disables network tracking, prevents network events from being sent to the client.
         */
        disable(): Promise<void>;

        /**
         * Activates emulation of network conditions.
         */
        emulateNetworkConditions(params: Protocol.Network.EmulateNetworkConditionsRequest): Promise<void>;

        /**
         * Enables network tracking, network events will now be delivered to the client.
         */
        enable(params: Protocol.Network.EnableRequest): Promise<void>;

        /**
         * Returns all browser cookies. Depending on the backend support, will return detailed cookie
         * information in the `cookies` field.
         * Deprecated. Use Storage.getCookies instead.
         */
        getAllCookies(): Promise<Protocol.Network.GetAllCookiesResponse>;

        /**
         * Returns the DER-encoded certificate.
         */
        getCertificate(params: Protocol.Network.GetCertificateRequest): Promise<Protocol.Network.GetCertificateResponse>;

        /**
         * Returns all browser cookies for the current URL. Depending on the backend support, will return
         * detailed cookie information in the `cookies` field.
         */
        getCookies(params: Protocol.Network.GetCookiesRequest): Promise<Protocol.Network.GetCookiesResponse>;

        /**
         * Returns content served for the given request.
         */
        getResponseBody(params: Protocol.Network.GetResponseBodyRequest): Promise<Protocol.Network.GetResponseBodyResponse>;

        /**
         * Returns post data sent with the request. Returns an error when no data was sent with the request.
         */
        getRequestPostData(params: Protocol.Network.GetRequestPostDataRequest): Promise<Protocol.Network.GetRequestPostDataResponse>;

        /**
         * Returns content served for the given currently intercepted request.
         */
        getResponseBodyForInterception(params: Protocol.Network.GetResponseBodyForInterceptionRequest): Promise<Protocol.Network.GetResponseBodyForInterceptionResponse>;

        /**
         * Returns a handle to the stream representing the response body. Note that after this command,
         * the intercepted request can't be continued as is -- you either need to cancel it or to provide
         * the response body. The stream only supports sequential read, IO.read will fail if the position
         * is specified.
         */
        takeResponseBodyForInterceptionAsStream(params: Protocol.Network.TakeResponseBodyForInterceptionAsStreamRequest): Promise<Protocol.Network.TakeResponseBodyForInterceptionAsStreamResponse>;

        /**
         * This method sends a new XMLHttpRequest which is identical to the original one. The following
         * parameters should be identical: method, url, async, request body, extra headers, withCredentials
         * attribute, user, password.
         */
        replayXHR(params: Protocol.Network.ReplayXHRRequest): Promise<void>;

        /**
         * Searches for given string in response content.
         */
        searchInResponseBody(params: Protocol.Network.SearchInResponseBodyRequest): Promise<Protocol.Network.SearchInResponseBodyResponse>;

        /**
         * Blocks URLs from loading.
         */
        setBlockedURLs(params: Protocol.Network.SetBlockedURLsRequest): Promise<void>;

        /**
         * Toggles ignoring of service worker for each request.
         */
        setBypassServiceWorker(params: Protocol.Network.SetBypassServiceWorkerRequest): Promise<void>;

        /**
         * Toggles ignoring cache for each request. If `true`, cache will not be used.
         */
        setCacheDisabled(params: Protocol.Network.SetCacheDisabledRequest): Promise<void>;

        /**
         * Sets a cookie with the given cookie data; may overwrite equivalent cookies if they exist.
         */
        setCookie(params: Protocol.Network.SetCookieRequest): Promise<Protocol.Network.SetCookieResponse>;

        /**
         * Sets given cookies.
         */
        setCookies(params: Protocol.Network.SetCookiesRequest): Promise<void>;

        /**
         * Specifies whether to always send extra HTTP headers with the requests from this page.
         */
        setExtraHTTPHeaders(params: Protocol.Network.SetExtraHTTPHeadersRequest): Promise<void>;

        /**
         * Specifies whether to attach a page script stack id in requests
         */
        setAttachDebugStack(params: Protocol.Network.SetAttachDebugStackRequest): Promise<void>;

        /**
         * Sets the requests to intercept that match the provided patterns and optionally resource types.
         * Deprecated, please use Fetch.enable instead.
         */
        setRequestInterception(params: Protocol.Network.SetRequestInterceptionRequest): Promise<void>;

        /**
         * Allows overriding user agent with the given string.
         */
        setUserAgentOverride(params: Protocol.Network.SetUserAgentOverrideRequest): Promise<void>;

        /**
         * Enables streaming of the response for the given requestId.
         * If enabled, the dataReceived event contains the data that was received during streaming.
         */
        streamResourceContent(params: Protocol.Network.StreamResourceContentRequest): Promise<Protocol.Network.StreamResourceContentResponse>;

        /**
         * Returns information about the COEP/COOP isolation status.
         */
        getSecurityIsolationStatus(params: Protocol.Network.GetSecurityIsolationStatusRequest): Promise<Protocol.Network.GetSecurityIsolationStatusResponse>;

        /**
         * Enables tracking for the Reporting API, events generated by the Reporting API will now be delivered to the client.
         * Enabling triggers 'reportingApiReportAdded' for all existing reports.
         */
        enableReportingApi(params: Protocol.Network.EnableReportingApiRequest): Promise<void>;

        /**
         * Fetches the resource and returns the content.
         */
        loadNetworkResource(params: Protocol.Network.LoadNetworkResourceRequest): Promise<Protocol.Network.LoadNetworkResourceResponse>;

        /**
         * Sets Controls for third-party cookie access
         * Page reload is required before the new cookie behavior will be observed
         */
        setCookieControls(params: Protocol.Network.SetCookieControlsRequest): Promise<void>;

        /**
         * Fired when data chunk was received over the network.
         */
        on(event: 'dataReceived', listener: (params: Protocol.Network.DataReceivedEvent) => void): void;

        /**
         * Fired when EventSource message is received.
         */
        on(event: 'eventSourceMessageReceived', listener: (params: Protocol.Network.EventSourceMessageReceivedEvent) => void): void;

        /**
         * Fired when HTTP request has failed to load.
         */
        on(event: 'loadingFailed', listener: (params: Protocol.Network.LoadingFailedEvent) => void): void;

        /**
         * Fired when HTTP request has finished loading.
         */
        on(event: 'loadingFinished', listener: (params: Protocol.Network.LoadingFinishedEvent) => void): void;

        /**
         * Details of an intercepted HTTP request, which must be either allowed, blocked, modified or
         * mocked.
         * Deprecated, use Fetch.requestPaused instead.
         */
        on(event: 'requestIntercepted', listener: (params: Protocol.Network.RequestInterceptedEvent) => void): void;

        /**
         * Fired if request ended up loading from cache.
         */
        on(event: 'requestServedFromCache', listener: (params: Protocol.Network.RequestServedFromCacheEvent) => void): void;

        /**
         * Fired when page is about to send HTTP request.
         */
        on(event: 'requestWillBeSent', listener: (params: Protocol.Network.RequestWillBeSentEvent) => void): void;

        /**
         * Fired when resource loading priority is changed
         */
        on(event: 'resourceChangedPriority', listener: (params: Protocol.Network.ResourceChangedPriorityEvent) => void): void;

        /**
         * Fired when a signed exchange was received over the network
         */
        on(event: 'signedExchangeReceived', listener: (params: Protocol.Network.SignedExchangeReceivedEvent) => void): void;

        /**
         * Fired when HTTP response is available.
         */
        on(event: 'responseReceived', listener: (params: Protocol.Network.ResponseReceivedEvent) => void): void;

        /**
         * Fired when WebSocket is closed.
         */
        on(event: 'webSocketClosed', listener: (params: Protocol.Network.WebSocketClosedEvent) => void): void;

        /**
         * Fired upon WebSocket creation.
         */
        on(event: 'webSocketCreated', listener: (params: Protocol.Network.WebSocketCreatedEvent) => void): void;

        /**
         * Fired when WebSocket message error occurs.
         */
        on(event: 'webSocketFrameError', listener: (params: Protocol.Network.WebSocketFrameErrorEvent) => void): void;

        /**
         * Fired when WebSocket message is received.
         */
        on(event: 'webSocketFrameReceived', listener: (params: Protocol.Network.WebSocketFrameReceivedEvent) => void): void;

        /**
         * Fired when WebSocket message is sent.
         */
        on(event: 'webSocketFrameSent', listener: (params: Protocol.Network.WebSocketFrameSentEvent) => void): void;

        /**
         * Fired when WebSocket handshake response becomes available.
         */
        on(event: 'webSocketHandshakeResponseReceived', listener: (params: Protocol.Network.WebSocketHandshakeResponseReceivedEvent) => void): void;

        /**
         * Fired when WebSocket is about to initiate handshake.
         */
        on(event: 'webSocketWillSendHandshakeRequest', listener: (params: Protocol.Network.WebSocketWillSendHandshakeRequestEvent) => void): void;

        /**
         * Fired upon WebTransport creation.
         */
        on(event: 'webTransportCreated', listener: (params: Protocol.Network.WebTransportCreatedEvent) => void): void;

        /**
         * Fired when WebTransport handshake is finished.
         */
        on(event: 'webTransportConnectionEstablished', listener: (params: Protocol.Network.WebTransportConnectionEstablishedEvent) => void): void;

        /**
         * Fired when WebTransport is disposed.
         */
        on(event: 'webTransportClosed', listener: (params: Protocol.Network.WebTransportClosedEvent) => void): void;

        /**
         * Fired upon direct_socket.TCPSocket creation.
         */
        on(event: 'directTCPSocketCreated', listener: (params: Protocol.Network.DirectTCPSocketCreatedEvent) => void): void;

        /**
         * Fired when direct_socket.TCPSocket connection is opened.
         */
        on(event: 'directTCPSocketOpened', listener: (params: Protocol.Network.DirectTCPSocketOpenedEvent) => void): void;

        /**
         * Fired when direct_socket.TCPSocket is aborted.
         */
        on(event: 'directTCPSocketAborted', listener: (params: Protocol.Network.DirectTCPSocketAbortedEvent) => void): void;

        /**
         * Fired when direct_socket.TCPSocket is closed.
         */
        on(event: 'directTCPSocketClosed', listener: (params: Protocol.Network.DirectTCPSocketClosedEvent) => void): void;

        /**
         * Fired when data is sent to tcp direct socket stream.
         */
        on(event: 'directTCPSocketChunkSent', listener: (params: Protocol.Network.DirectTCPSocketChunkSentEvent) => void): void;

        /**
         * Fired when data is received from tcp direct socket stream.
         */
        on(event: 'directTCPSocketChunkReceived', listener: (params: Protocol.Network.DirectTCPSocketChunkReceivedEvent) => void): void;

        /**
         * Fired upon direct_socket.UDPSocket creation.
         */
        on(event: 'directUDPSocketCreated', listener: (params: Protocol.Network.DirectUDPSocketCreatedEvent) => void): void;

        /**
         * Fired when direct_socket.UDPSocket connection is opened.
         */
        on(event: 'directUDPSocketOpened', listener: (params: Protocol.Network.DirectUDPSocketOpenedEvent) => void): void;

        /**
         * Fired when direct_socket.UDPSocket is aborted.
         */
        on(event: 'directUDPSocketAborted', listener: (params: Protocol.Network.DirectUDPSocketAbortedEvent) => void): void;

        /**
         * Fired when direct_socket.UDPSocket is closed.
         */
        on(event: 'directUDPSocketClosed', listener: (params: Protocol.Network.DirectUDPSocketClosedEvent) => void): void;

        /**
         * Fired when message is sent to udp direct socket stream.
         */
        on(event: 'directUDPSocketChunkSent', listener: (params: Protocol.Network.DirectUDPSocketChunkSentEvent) => void): void;

        /**
         * Fired when message is received from udp direct socket stream.
         */
        on(event: 'directUDPSocketChunkReceived', listener: (params: Protocol.Network.DirectUDPSocketChunkReceivedEvent) => void): void;

        /**
         * Fired when additional information about a requestWillBeSent event is available from the
         * network stack. Not every requestWillBeSent event will have an additional
         * requestWillBeSentExtraInfo fired for it, and there is no guarantee whether requestWillBeSent
         * or requestWillBeSentExtraInfo will be fired first for the same request.
         */
        on(event: 'requestWillBeSentExtraInfo', listener: (params: Protocol.Network.RequestWillBeSentExtraInfoEvent) => void): void;

        /**
         * Fired when additional information about a responseReceived event is available from the network
         * stack. Not every responseReceived event will have an additional responseReceivedExtraInfo for
         * it, and responseReceivedExtraInfo may be fired before or after responseReceived.
         */
        on(event: 'responseReceivedExtraInfo', listener: (params: Protocol.Network.ResponseReceivedExtraInfoEvent) => void): void;

        /**
         * Fired when 103 Early Hints headers is received in addition to the common response.
         * Not every responseReceived event will have an responseReceivedEarlyHints fired.
         * Only one responseReceivedEarlyHints may be fired for eached responseReceived event.
         */
        on(event: 'responseReceivedEarlyHints', listener: (params: Protocol.Network.ResponseReceivedEarlyHintsEvent) => void): void;

        /**
         * Fired exactly once for each Trust Token operation. Depending on
         * the type of the operation and whether the operation succeeded or
         * failed, the event is fired before the corresponding request was sent
         * or after the response was received.
         */
        on(event: 'trustTokenOperationDone', listener: (params: Protocol.Network.TrustTokenOperationDoneEvent) => void): void;

        /**
         * Fired once security policy has been updated.
         */
        on(event: 'policyUpdated', listener: () => void): void;

        /**
         * Fired once when parsing the .wbn file has succeeded.
         * The event contains the information about the web bundle contents.
         */
        on(event: 'subresourceWebBundleMetadataReceived', listener: (params: Protocol.Network.SubresourceWebBundleMetadataReceivedEvent) => void): void;

        /**
         * Fired once when parsing the .wbn file has failed.
         */
        on(event: 'subresourceWebBundleMetadataError', listener: (params: Protocol.Network.SubresourceWebBundleMetadataErrorEvent) => void): void;

        /**
         * Fired when handling requests for resources within a .wbn file.
         * Note: this will only be fired for resources that are requested by the webpage.
         */
        on(event: 'subresourceWebBundleInnerResponseParsed', listener: (params: Protocol.Network.SubresourceWebBundleInnerResponseParsedEvent) => void): void;

        /**
         * Fired when request for resources within a .wbn file failed.
         */
        on(event: 'subresourceWebBundleInnerResponseError', listener: (params: Protocol.Network.SubresourceWebBundleInnerResponseErrorEvent) => void): void;

        /**
         * Is sent whenever a new report is added.
         * And after 'enableReportingApi' for all existing reports.
         */
        on(event: 'reportingApiReportAdded', listener: (params: Protocol.Network.ReportingApiReportAddedEvent) => void): void;

        on(event: 'reportingApiReportUpdated', listener: (params: Protocol.Network.ReportingApiReportUpdatedEvent) => void): void;

        on(event: 'reportingApiEndpointsChangedForOrigin', listener: (params: Protocol.Network.ReportingApiEndpointsChangedForOriginEvent) => void): void;

    }

    export interface OverlayApi {
        /**
         * Disables domain notifications.
         */
        disable(): Promise<void>;

        /**
         * Enables domain notifications.
         */
        enable(): Promise<void>;

        /**
         * For testing.
         */
        getHighlightObjectForTest(params: Protocol.Overlay.GetHighlightObjectForTestRequest): Promise<Protocol.Overlay.GetHighlightObjectForTestResponse>;

        /**
         * For Persistent Grid testing.
         */
        getGridHighlightObjectsForTest(params: Protocol.Overlay.GetGridHighlightObjectsForTestRequest): Promise<Protocol.Overlay.GetGridHighlightObjectsForTestResponse>;

        /**
         * For Source Order Viewer testing.
         */
        getSourceOrderHighlightObjectForTest(params: Protocol.Overlay.GetSourceOrderHighlightObjectForTestRequest): Promise<Protocol.Overlay.GetSourceOrderHighlightObjectForTestResponse>;

        /**
         * Hides any highlight.
         */
        hideHighlight(): Promise<void>;

        /**
         * Highlights owner element of the frame with given id.
         * Deprecated: Doesn't work reliably and cannot be fixed due to process
         * separation (the owner node might be in a different process). Determine
         * the owner node in the client and use highlightNode.
         */
        highlightFrame(params: Protocol.Overlay.HighlightFrameRequest): Promise<void>;

        /**
         * Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or
         * objectId must be specified.
         */
        highlightNode(params: Protocol.Overlay.HighlightNodeRequest): Promise<void>;

        /**
         * Highlights given quad. Coordinates are absolute with respect to the main frame viewport.
         */
        highlightQuad(params: Protocol.Overlay.HighlightQuadRequest): Promise<void>;

        /**
         * Highlights given rectangle. Coordinates are absolute with respect to the main frame viewport.
         */
        highlightRect(params: Protocol.Overlay.HighlightRectRequest): Promise<void>;

        /**
         * Highlights the source order of the children of the DOM node with given id or with the given
         * JavaScript object wrapper. Either nodeId or objectId must be specified.
         */
        highlightSourceOrder(params: Protocol.Overlay.HighlightSourceOrderRequest): Promise<void>;

        /**
         * Enters the 'inspect' mode. In this mode, elements that user is hovering over are highlighted.
         * Backend then generates 'inspectNodeRequested' event upon element selection.
         */
        setInspectMode(params: Protocol.Overlay.SetInspectModeRequest): Promise<void>;

        /**
         * Highlights owner element of all frames detected to be ads.
         */
        setShowAdHighlights(params: Protocol.Overlay.SetShowAdHighlightsRequest): Promise<void>;

        setPausedInDebuggerMessage(params: Protocol.Overlay.SetPausedInDebuggerMessageRequest): Promise<void>;

        /**
         * Requests that backend shows debug borders on layers
         */
        setShowDebugBorders(params: Protocol.Overlay.SetShowDebugBordersRequest): Promise<void>;

        /**
         * Requests that backend shows the FPS counter
         */
        setShowFPSCounter(params: Protocol.Overlay.SetShowFPSCounterRequest): Promise<void>;

        /**
         * Highlight multiple elements with the CSS Grid overlay.
         */
        setShowGridOverlays(params: Protocol.Overlay.SetShowGridOverlaysRequest): Promise<void>;

        setShowFlexOverlays(params: Protocol.Overlay.SetShowFlexOverlaysRequest): Promise<void>;

        setShowScrollSnapOverlays(params: Protocol.Overlay.SetShowScrollSnapOverlaysRequest): Promise<void>;

        setShowContainerQueryOverlays(params: Protocol.Overlay.SetShowContainerQueryOverlaysRequest): Promise<void>;

        /**
         * Requests that backend shows paint rectangles
         */
        setShowPaintRects(params: Protocol.Overlay.SetShowPaintRectsRequest): Promise<void>;

        /**
         * Requests that backend shows layout shift regions
         */
        setShowLayoutShiftRegions(params: Protocol.Overlay.SetShowLayoutShiftRegionsRequest): Promise<void>;

        /**
         * Requests that backend shows scroll bottleneck rects
         */
        setShowScrollBottleneckRects(params: Protocol.Overlay.SetShowScrollBottleneckRectsRequest): Promise<void>;

        /**
         * Deprecated, no longer has any effect.
         */
        setShowHitTestBorders(params: Protocol.Overlay.SetShowHitTestBordersRequest): Promise<void>;

        /**
         * Deprecated, no longer has any effect.
         */
        setShowWebVitals(params: Protocol.Overlay.SetShowWebVitalsRequest): Promise<void>;

        /**
         * Paints viewport size upon main frame resize.
         */
        setShowViewportSizeOnResize(params: Protocol.Overlay.SetShowViewportSizeOnResizeRequest): Promise<void>;

        /**
         * Add a dual screen device hinge
         */
        setShowHinge(params: Protocol.Overlay.SetShowHingeRequest): Promise<void>;

        /**
         * Show elements in isolation mode with overlays.
         */
        setShowIsolatedElements(params: Protocol.Overlay.SetShowIsolatedElementsRequest): Promise<void>;

        /**
         * Show Window Controls Overlay for PWA
         */
        setShowWindowControlsOverlay(params: Protocol.Overlay.SetShowWindowControlsOverlayRequest): Promise<void>;

        /**
         * Fired when the node should be inspected. This happens after call to `setInspectMode` or when
         * user manually inspects an element.
         */
        on(event: 'inspectNodeRequested', listener: (params: Protocol.Overlay.InspectNodeRequestedEvent) => void): void;

        /**
         * Fired when the node should be highlighted. This happens after call to `setInspectMode`.
         */
        on(event: 'nodeHighlightRequested', listener: (params: Protocol.Overlay.NodeHighlightRequestedEvent) => void): void;

        /**
         * Fired when user asks to capture screenshot of some area on the page.
         */
        on(event: 'screenshotRequested', listener: (params: Protocol.Overlay.ScreenshotRequestedEvent) => void): void;

        /**
         * Fired when user cancels the inspect mode.
         */
        on(event: 'inspectModeCanceled', listener: () => void): void;

    }

    export interface PageApi {
        /**
         * Deprecated, please use addScriptToEvaluateOnNewDocument instead.
         */
        addScriptToEvaluateOnLoad(params: Protocol.Page.AddScriptToEvaluateOnLoadRequest): Promise<Protocol.Page.AddScriptToEvaluateOnLoadResponse>;

        /**
         * Evaluates given script in every frame upon creation (before loading frame's scripts).
         */
        addScriptToEvaluateOnNewDocument(params: Protocol.Page.AddScriptToEvaluateOnNewDocumentRequest): Promise<Protocol.Page.AddScriptToEvaluateOnNewDocumentResponse>;

        /**
         * Brings page to front (activates tab).
         */
        bringToFront(): Promise<void>;

        /**
         * Capture page screenshot.
         */
        captureScreenshot(params: Protocol.Page.CaptureScreenshotRequest): Promise<Protocol.Page.CaptureScreenshotResponse>;

        /**
         * Returns a snapshot of the page as a string. For MHTML format, the serialization includes
         * iframes, shadow DOM, external resources, and element-inline styles.
         */
        captureSnapshot(params: Protocol.Page.CaptureSnapshotRequest): Promise<Protocol.Page.CaptureSnapshotResponse>;

        /**
         * Clears the overridden device metrics.
         */
        clearDeviceMetricsOverride(): Promise<void>;

        /**
         * Clears the overridden Device Orientation.
         */
        clearDeviceOrientationOverride(): Promise<void>;

        /**
         * Clears the overridden Geolocation Position and Error.
         */
        clearGeolocationOverride(): Promise<void>;

        /**
         * Creates an isolated world for the given frame.
         */
        createIsolatedWorld(params: Protocol.Page.CreateIsolatedWorldRequest): Promise<Protocol.Page.CreateIsolatedWorldResponse>;

        /**
         * Deletes browser cookie with given name, domain and path.
         */
        deleteCookie(params: Protocol.Page.DeleteCookieRequest): Promise<void>;

        /**
         * Disables page domain notifications.
         */
        disable(): Promise<void>;

        /**
         * Enables page domain notifications.
         */
        enable(params: Protocol.Page.EnableRequest): Promise<void>;

        /**
         * Gets the processed manifest for this current document.
         *   This API always waits for the manifest to be loaded.
         *   If manifestId is provided, and it does not match the manifest of the
         *     current document, this API errors out.
         *   If there is not a loaded page, this API errors out immediately.
         */
        getAppManifest(params: Protocol.Page.GetAppManifestRequest): Promise<Protocol.Page.GetAppManifestResponse>;

        getInstallabilityErrors(): Promise<Protocol.Page.GetInstallabilityErrorsResponse>;

        /**
         * Deprecated because it's not guaranteed that the returned icon is in fact the one used for PWA installation.
         */
        getManifestIcons(): Promise<Protocol.Page.GetManifestIconsResponse>;

        /**
         * Returns the unique (PWA) app id.
         * Only returns values if the feature flag 'WebAppEnableManifestId' is enabled
         */
        getAppId(): Promise<Protocol.Page.GetAppIdResponse>;

        getAdScriptAncestryIds(params: Protocol.Page.GetAdScriptAncestryIdsRequest): Promise<Protocol.Page.GetAdScriptAncestryIdsResponse>;

        /**
         * Returns present frame tree structure.
         */
        getFrameTree(): Promise<Protocol.Page.GetFrameTreeResponse>;

        /**
         * Returns metrics relating to the layouting of the page, such as viewport bounds/scale.
         */
        getLayoutMetrics(): Promise<Protocol.Page.GetLayoutMetricsResponse>;

        /**
         * Returns navigation history for the current page.
         */
        getNavigationHistory(): Promise<Protocol.Page.GetNavigationHistoryResponse>;

        /**
         * Resets navigation history for the current page.
         */
        resetNavigationHistory(): Promise<void>;

        /**
         * Returns content of the given resource.
         */
        getResourceContent(params: Protocol.Page.GetResourceContentRequest): Promise<Protocol.Page.GetResourceContentResponse>;

        /**
         * Returns present frame / resource tree structure.
         */
        getResourceTree(): Promise<Protocol.Page.GetResourceTreeResponse>;

        /**
         * Accepts or dismisses a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload).
         */
        handleJavaScriptDialog(params: Protocol.Page.HandleJavaScriptDialogRequest): Promise<void>;

        /**
         * Navigates current page to the given URL.
         */
        navigate(params: Protocol.Page.NavigateRequest): Promise<Protocol.Page.NavigateResponse>;

        /**
         * Navigates current page to the given history entry.
         */
        navigateToHistoryEntry(params: Protocol.Page.NavigateToHistoryEntryRequest): Promise<void>;

        /**
         * Print page as PDF.
         */
        printToPDF(params: Protocol.Page.PrintToPDFRequest): Promise<Protocol.Page.PrintToPDFResponse>;

        /**
         * Reloads given page optionally ignoring the cache.
         */
        reload(params: Protocol.Page.ReloadRequest): Promise<void>;

        /**
         * Deprecated, please use removeScriptToEvaluateOnNewDocument instead.
         */
        removeScriptToEvaluateOnLoad(params: Protocol.Page.RemoveScriptToEvaluateOnLoadRequest): Promise<void>;

        /**
         * Removes given script from the list.
         */
        removeScriptToEvaluateOnNewDocument(params: Protocol.Page.RemoveScriptToEvaluateOnNewDocumentRequest): Promise<void>;

        /**
         * Acknowledges that a screencast frame has been received by the frontend.
         */
        screencastFrameAck(params: Protocol.Page.ScreencastFrameAckRequest): Promise<void>;

        /**
         * Searches for given string in resource content.
         */
        searchInResource(params: Protocol.Page.SearchInResourceRequest): Promise<Protocol.Page.SearchInResourceResponse>;

        /**
         * Enable Chrome's experimental ad filter on all sites.
         */
        setAdBlockingEnabled(params: Protocol.Page.SetAdBlockingEnabledRequest): Promise<void>;

        /**
         * Enable page Content Security Policy by-passing.
         */
        setBypassCSP(params: Protocol.Page.SetBypassCSPRequest): Promise<void>;

        /**
         * Get Permissions Policy state on given frame.
         */
        getPermissionsPolicyState(params: Protocol.Page.GetPermissionsPolicyStateRequest): Promise<Protocol.Page.GetPermissionsPolicyStateResponse>;

        /**
         * Get Origin Trials on given frame.
         */
        getOriginTrials(params: Protocol.Page.GetOriginTrialsRequest): Promise<Protocol.Page.GetOriginTrialsResponse>;

        /**
         * Overrides the values of device screen dimensions (window.screen.width, window.screen.height,
         * window.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media
         * query results).
         */
        setDeviceMetricsOverride(params: Protocol.Page.SetDeviceMetricsOverrideRequest): Promise<void>;

        /**
         * Overrides the Device Orientation.
         */
        setDeviceOrientationOverride(params: Protocol.Page.SetDeviceOrientationOverrideRequest): Promise<void>;

        /**
         * Set generic font families.
         */
        setFontFamilies(params: Protocol.Page.SetFontFamiliesRequest): Promise<void>;

        /**
         * Set default font sizes.
         */
        setFontSizes(params: Protocol.Page.SetFontSizesRequest): Promise<void>;

        /**
         * Sets given markup as the document's HTML.
         */
        setDocumentContent(params: Protocol.Page.SetDocumentContentRequest): Promise<void>;

        /**
         * Set the behavior when downloading a file.
         */
        setDownloadBehavior(params: Protocol.Page.SetDownloadBehaviorRequest): Promise<void>;

        /**
         * Overrides the Geolocation Position or Error. Omitting any of the parameters emulates position
         * unavailable.
         */
        setGeolocationOverride(params: Protocol.Page.SetGeolocationOverrideRequest): Promise<void>;

        /**
         * Controls whether page will emit lifecycle events.
         */
        setLifecycleEventsEnabled(params: Protocol.Page.SetLifecycleEventsEnabledRequest): Promise<void>;

        /**
         * Toggles mouse event-based touch event emulation.
         */
        setTouchEmulationEnabled(params: Protocol.Page.SetTouchEmulationEnabledRequest): Promise<void>;

        /**
         * Starts sending each frame using the `screencastFrame` event.
         */
        startScreencast(params: Protocol.Page.StartScreencastRequest): Promise<void>;

        /**
         * Force the page stop all navigations and pending resource fetches.
         */
        stopLoading(): Promise<void>;

        /**
         * Crashes renderer on the IO thread, generates minidumps.
         */
        crash(): Promise<void>;

        /**
         * Tries to close page, running its beforeunload hooks, if any.
         */
        close(): Promise<void>;

        /**
         * Tries to update the web lifecycle state of the page.
         * It will transition the page to the given state according to:
         * https://github.com/WICG/web-lifecycle/
         */
        setWebLifecycleState(params: Protocol.Page.SetWebLifecycleStateRequest): Promise<void>;

        /**
         * Stops sending each frame in the `screencastFrame`.
         */
        stopScreencast(): Promise<void>;

        /**
         * Requests backend to produce compilation cache for the specified scripts.
         * `scripts` are appended to the list of scripts for which the cache
         * would be produced. The list may be reset during page navigation.
         * When script with a matching URL is encountered, the cache is optionally
         * produced upon backend discretion, based on internal heuristics.
         * See also: `Page.compilationCacheProduced`.
         */
        produceCompilationCache(params: Protocol.Page.ProduceCompilationCacheRequest): Promise<void>;

        /**
         * Seeds compilation cache for given url. Compilation cache does not survive
         * cross-process navigation.
         */
        addCompilationCache(params: Protocol.Page.AddCompilationCacheRequest): Promise<void>;

        /**
         * Clears seeded compilation cache.
         */
        clearCompilationCache(): Promise<void>;

        /**
         * Sets the Secure Payment Confirmation transaction mode.
         * https://w3c.github.io/secure-payment-confirmation/#sctn-automation-set-spc-transaction-mode
         */
        setSPCTransactionMode(params: Protocol.Page.SetSPCTransactionModeRequest): Promise<void>;

        /**
         * Extensions for Custom Handlers API:
         * https://html.spec.whatwg.org/multipage/system-state.html#rph-automation
         */
        setRPHRegistrationMode(params: Protocol.Page.SetRPHRegistrationModeRequest): Promise<void>;

        /**
         * Generates a report for testing.
         */
        generateTestReport(params: Protocol.Page.GenerateTestReportRequest): Promise<void>;

        /**
         * Pauses page execution. Can be resumed using generic Runtime.runIfWaitingForDebugger.
         */
        waitForDebugger(): Promise<void>;

        /**
         * Intercept file chooser requests and transfer control to protocol clients.
         * When file chooser interception is enabled, native file chooser dialog is not shown.
         * Instead, a protocol event `Page.fileChooserOpened` is emitted.
         */
        setInterceptFileChooserDialog(params: Protocol.Page.SetInterceptFileChooserDialogRequest): Promise<void>;

        /**
         * Enable/disable prerendering manually.
         * 
         * This command is a short-term solution for https://crbug.com/1440085.
         * See https://docs.google.com/document/d/12HVmFxYj5Jc-eJr5OmWsa2bqTJsbgGLKI6ZIyx0_wpA
         * for more details.
         * 
         * TODO(https://crbug.com/1440085): Remove this once Puppeteer supports tab targets.
         */
        setPrerenderingAllowed(params: Protocol.Page.SetPrerenderingAllowedRequest): Promise<void>;

        on(event: 'domContentEventFired', listener: (params: Protocol.Page.DomContentEventFiredEvent) => void): void;

        /**
         * Emitted only when `page.interceptFileChooser` is enabled.
         */
        on(event: 'fileChooserOpened', listener: (params: Protocol.Page.FileChooserOpenedEvent) => void): void;

        /**
         * Fired when frame has been attached to its parent.
         */
        on(event: 'frameAttached', listener: (params: Protocol.Page.FrameAttachedEvent) => void): void;

        /**
         * Fired when frame no longer has a scheduled navigation.
         */
        on(event: 'frameClearedScheduledNavigation', listener: (params: Protocol.Page.FrameClearedScheduledNavigationEvent) => void): void;

        /**
         * Fired when frame has been detached from its parent.
         */
        on(event: 'frameDetached', listener: (params: Protocol.Page.FrameDetachedEvent) => void): void;

        /**
         * Fired before frame subtree is detached. Emitted before any frame of the
         * subtree is actually detached.
         */
        on(event: 'frameSubtreeWillBeDetached', listener: (params: Protocol.Page.FrameSubtreeWillBeDetachedEvent) => void): void;

        /**
         * Fired once navigation of the frame has completed. Frame is now associated with the new loader.
         */
        on(event: 'frameNavigated', listener: (params: Protocol.Page.FrameNavigatedEvent) => void): void;

        /**
         * Fired when opening document to write to.
         */
        on(event: 'documentOpened', listener: (params: Protocol.Page.DocumentOpenedEvent) => void): void;

        on(event: 'frameResized', listener: () => void): void;

        /**
         * Fired when a navigation starts. This event is fired for both
         * renderer-initiated and browser-initiated navigations. For renderer-initiated
         * navigations, the event is fired after `frameRequestedNavigation`.
         * Navigation may still be cancelled after the event is issued. Multiple events
         * can be fired for a single navigation, for example, when a same-document
         * navigation becomes a cross-document navigation (such as in the case of a
         * frameset).
         */
        on(event: 'frameStartedNavigating', listener: (params: Protocol.Page.FrameStartedNavigatingEvent) => void): void;

        /**
         * Fired when a renderer-initiated navigation is requested.
         * Navigation may still be cancelled after the event is issued.
         */
        on(event: 'frameRequestedNavigation', listener: (params: Protocol.Page.FrameRequestedNavigationEvent) => void): void;

        /**
         * Fired when frame schedules a potential navigation.
         */
        on(event: 'frameScheduledNavigation', listener: (params: Protocol.Page.FrameScheduledNavigationEvent) => void): void;

        /**
         * Fired when frame has started loading.
         */
        on(event: 'frameStartedLoading', listener: (params: Protocol.Page.FrameStartedLoadingEvent) => void): void;

        /**
         * Fired when frame has stopped loading.
         */
        on(event: 'frameStoppedLoading', listener: (params: Protocol.Page.FrameStoppedLoadingEvent) => void): void;

        /**
         * Fired when page is about to start a download.
         * Deprecated. Use Browser.downloadWillBegin instead.
         */
        on(event: 'downloadWillBegin', listener: (params: Protocol.Page.DownloadWillBeginEvent) => void): void;

        /**
         * Fired when download makes progress. Last call has |done| == true.
         * Deprecated. Use Browser.downloadProgress instead.
         */
        on(event: 'downloadProgress', listener: (params: Protocol.Page.DownloadProgressEvent) => void): void;

        /**
         * Fired when interstitial page was hidden
         */
        on(event: 'interstitialHidden', listener: () => void): void;

        /**
         * Fired when interstitial page was shown
         */
        on(event: 'interstitialShown', listener: () => void): void;

        /**
         * Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) has been
         * closed.
         */
        on(event: 'javascriptDialogClosed', listener: (params: Protocol.Page.JavascriptDialogClosedEvent) => void): void;

        /**
         * Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) is about to
         * open.
         */
        on(event: 'javascriptDialogOpening', listener: (params: Protocol.Page.JavascriptDialogOpeningEvent) => void): void;

        /**
         * Fired for lifecycle events (navigation, load, paint, etc) in the current
         * target (including local frames).
         */
        on(event: 'lifecycleEvent', listener: (params: Protocol.Page.LifecycleEventEvent) => void): void;

        /**
         * Fired for failed bfcache history navigations if BackForwardCache feature is enabled. Do
         * not assume any ordering with the Page.frameNavigated event. This event is fired only for
         * main-frame history navigation where the document changes (non-same-document navigations),
         * when bfcache navigation fails.
         */
        on(event: 'backForwardCacheNotUsed', listener: (params: Protocol.Page.BackForwardCacheNotUsedEvent) => void): void;

        on(event: 'loadEventFired', listener: (params: Protocol.Page.LoadEventFiredEvent) => void): void;

        /**
         * Fired when same-document navigation happens, e.g. due to history API usage or anchor navigation.
         */
        on(event: 'navigatedWithinDocument', listener: (params: Protocol.Page.NavigatedWithinDocumentEvent) => void): void;

        /**
         * Compressed image data requested by the `startScreencast`.
         */
        on(event: 'screencastFrame', listener: (params: Protocol.Page.ScreencastFrameEvent) => void): void;

        /**
         * Fired when the page with currently enabled screencast was shown or hidden `.
         */
        on(event: 'screencastVisibilityChanged', listener: (params: Protocol.Page.ScreencastVisibilityChangedEvent) => void): void;

        /**
         * Fired when a new window is going to be opened, via window.open(), link click, form submission,
         * etc.
         */
        on(event: 'windowOpen', listener: (params: Protocol.Page.WindowOpenEvent) => void): void;

        /**
         * Issued for every compilation cache generated. Is only available
         * if Page.setGenerateCompilationCache is enabled.
         */
        on(event: 'compilationCacheProduced', listener: (params: Protocol.Page.CompilationCacheProducedEvent) => void): void;

    }

    export interface PerformanceApi {
        /**
         * Disable collecting and reporting metrics.
         */
        disable(): Promise<void>;

        /**
         * Enable collecting and reporting metrics.
         */
        enable(params: Protocol.Performance.EnableRequest): Promise<void>;

        /**
         * Sets time domain to use for collecting and reporting duration metrics.
         * Note that this must be called before enabling metrics collection. Calling
         * this method while metrics collection is enabled returns an error.
         */
        setTimeDomain(params: Protocol.Performance.SetTimeDomainRequest): Promise<void>;

        /**
         * Retrieve current values of run-time metrics.
         */
        getMetrics(): Promise<Protocol.Performance.GetMetricsResponse>;

        /**
         * Current values of the metrics.
         */
        on(event: 'metrics', listener: (params: Protocol.Performance.MetricsEvent) => void): void;

    }

    export interface PerformanceTimelineApi {
        /**
         * Previously buffered events would be reported before method returns.
         * See also: timelineEventAdded
         */
        enable(params: Protocol.PerformanceTimeline.EnableRequest): Promise<void>;

        /**
         * Sent when a performance timeline event is added. See reportPerformanceTimeline method.
         */
        on(event: 'timelineEventAdded', listener: (params: Protocol.PerformanceTimeline.TimelineEventAddedEvent) => void): void;

    }

    export interface SecurityApi {
        /**
         * Disables tracking security state changes.
         */
        disable(): Promise<void>;

        /**
         * Enables tracking security state changes.
         */
        enable(): Promise<void>;

        /**
         * Enable/disable whether all certificate errors should be ignored.
         */
        setIgnoreCertificateErrors(params: Protocol.Security.SetIgnoreCertificateErrorsRequest): Promise<void>;

        /**
         * Handles a certificate error that fired a certificateError event.
         */
        handleCertificateError(params: Protocol.Security.HandleCertificateErrorRequest): Promise<void>;

        /**
         * Enable/disable overriding certificate errors. If enabled, all certificate error events need to
         * be handled by the DevTools client and should be answered with `handleCertificateError` commands.
         */
        setOverrideCertificateErrors(params: Protocol.Security.SetOverrideCertificateErrorsRequest): Promise<void>;

        /**
         * There is a certificate error. If overriding certificate errors is enabled, then it should be
         * handled with the `handleCertificateError` command. Note: this event does not fire if the
         * certificate error has been allowed internally. Only one client per target should override
         * certificate errors at the same time.
         */
        on(event: 'certificateError', listener: (params: Protocol.Security.CertificateErrorEvent) => void): void;

        /**
         * The security state of the page changed.
         */
        on(event: 'visibleSecurityStateChanged', listener: (params: Protocol.Security.VisibleSecurityStateChangedEvent) => void): void;

        /**
         * The security state of the page changed. No longer being sent.
         */
        on(event: 'securityStateChanged', listener: (params: Protocol.Security.SecurityStateChangedEvent) => void): void;

    }

    export interface ServiceWorkerApi {
        deliverPushMessage(params: Protocol.ServiceWorker.DeliverPushMessageRequest): Promise<void>;

        disable(): Promise<void>;

        dispatchSyncEvent(params: Protocol.ServiceWorker.DispatchSyncEventRequest): Promise<void>;

        dispatchPeriodicSyncEvent(params: Protocol.ServiceWorker.DispatchPeriodicSyncEventRequest): Promise<void>;

        enable(): Promise<void>;

        inspectWorker(params: Protocol.ServiceWorker.InspectWorkerRequest): Promise<void>;

        setForceUpdateOnPageLoad(params: Protocol.ServiceWorker.SetForceUpdateOnPageLoadRequest): Promise<void>;

        skipWaiting(params: Protocol.ServiceWorker.SkipWaitingRequest): Promise<void>;

        startWorker(params: Protocol.ServiceWorker.StartWorkerRequest): Promise<void>;

        stopAllWorkers(): Promise<void>;

        stopWorker(params: Protocol.ServiceWorker.StopWorkerRequest): Promise<void>;

        unregister(params: Protocol.ServiceWorker.UnregisterRequest): Promise<void>;

        updateRegistration(params: Protocol.ServiceWorker.UpdateRegistrationRequest): Promise<void>;

        on(event: 'workerErrorReported', listener: (params: Protocol.ServiceWorker.WorkerErrorReportedEvent) => void): void;

        on(event: 'workerRegistrationUpdated', listener: (params: Protocol.ServiceWorker.WorkerRegistrationUpdatedEvent) => void): void;

        on(event: 'workerVersionUpdated', listener: (params: Protocol.ServiceWorker.WorkerVersionUpdatedEvent) => void): void;

    }

    export interface StorageApi {
        /**
         * Returns a storage key given a frame id.
         */
        getStorageKeyForFrame(params: Protocol.Storage.GetStorageKeyForFrameRequest): Promise<Protocol.Storage.GetStorageKeyForFrameResponse>;

        /**
         * Clears storage for origin.
         */
        clearDataForOrigin(params: Protocol.Storage.ClearDataForOriginRequest): Promise<void>;

        /**
         * Clears storage for storage key.
         */
        clearDataForStorageKey(params: Protocol.Storage.ClearDataForStorageKeyRequest): Promise<void>;

        /**
         * Returns all browser cookies.
         */
        getCookies(params: Protocol.Storage.GetCookiesRequest): Promise<Protocol.Storage.GetCookiesResponse>;

        /**
         * Sets given cookies.
         */
        setCookies(params: Protocol.Storage.SetCookiesRequest): Promise<void>;

        /**
         * Clears cookies.
         */
        clearCookies(params: Protocol.Storage.ClearCookiesRequest): Promise<void>;

        /**
         * Returns usage and quota in bytes.
         */
        getUsageAndQuota(params: Protocol.Storage.GetUsageAndQuotaRequest): Promise<Protocol.Storage.GetUsageAndQuotaResponse>;

        /**
         * Override quota for the specified origin
         */
        overrideQuotaForOrigin(params: Protocol.Storage.OverrideQuotaForOriginRequest): Promise<void>;

        /**
         * Registers origin to be notified when an update occurs to its cache storage list.
         */
        trackCacheStorageForOrigin(params: Protocol.Storage.TrackCacheStorageForOriginRequest): Promise<void>;

        /**
         * Registers storage key to be notified when an update occurs to its cache storage list.
         */
        trackCacheStorageForStorageKey(params: Protocol.Storage.TrackCacheStorageForStorageKeyRequest): Promise<void>;

        /**
         * Registers origin to be notified when an update occurs to its IndexedDB.
         */
        trackIndexedDBForOrigin(params: Protocol.Storage.TrackIndexedDBForOriginRequest): Promise<void>;

        /**
         * Registers storage key to be notified when an update occurs to its IndexedDB.
         */
        trackIndexedDBForStorageKey(params: Protocol.Storage.TrackIndexedDBForStorageKeyRequest): Promise<void>;

        /**
         * Unregisters origin from receiving notifications for cache storage.
         */
        untrackCacheStorageForOrigin(params: Protocol.Storage.UntrackCacheStorageForOriginRequest): Promise<void>;

        /**
         * Unregisters storage key from receiving notifications for cache storage.
         */
        untrackCacheStorageForStorageKey(params: Protocol.Storage.UntrackCacheStorageForStorageKeyRequest): Promise<void>;

        /**
         * Unregisters origin from receiving notifications for IndexedDB.
         */
        untrackIndexedDBForOrigin(params: Protocol.Storage.UntrackIndexedDBForOriginRequest): Promise<void>;

        /**
         * Unregisters storage key from receiving notifications for IndexedDB.
         */
        untrackIndexedDBForStorageKey(params: Protocol.Storage.UntrackIndexedDBForStorageKeyRequest): Promise<void>;

        /**
         * Returns the number of stored Trust Tokens per issuer for the
         * current browsing context.
         */
        getTrustTokens(): Promise<Protocol.Storage.GetTrustTokensResponse>;

        /**
         * Removes all Trust Tokens issued by the provided issuerOrigin.
         * Leaves other stored data, including the issuer's Redemption Records, intact.
         */
        clearTrustTokens(params: Protocol.Storage.ClearTrustTokensRequest): Promise<Protocol.Storage.ClearTrustTokensResponse>;

        /**
         * Gets details for a named interest group.
         */
        getInterestGroupDetails(params: Protocol.Storage.GetInterestGroupDetailsRequest): Promise<Protocol.Storage.GetInterestGroupDetailsResponse>;

        /**
         * Enables/Disables issuing of interestGroupAccessed events.
         */
        setInterestGroupTracking(params: Protocol.Storage.SetInterestGroupTrackingRequest): Promise<void>;

        /**
         * Enables/Disables issuing of interestGroupAuctionEventOccurred and
         * interestGroupAuctionNetworkRequestCreated.
         */
        setInterestGroupAuctionTracking(params: Protocol.Storage.SetInterestGroupAuctionTrackingRequest): Promise<void>;

        /**
         * Gets metadata for an origin's shared storage.
         */
        getSharedStorageMetadata(params: Protocol.Storage.GetSharedStorageMetadataRequest): Promise<Protocol.Storage.GetSharedStorageMetadataResponse>;

        /**
         * Gets the entries in an given origin's shared storage.
         */
        getSharedStorageEntries(params: Protocol.Storage.GetSharedStorageEntriesRequest): Promise<Protocol.Storage.GetSharedStorageEntriesResponse>;

        /**
         * Sets entry with `key` and `value` for a given origin's shared storage.
         */
        setSharedStorageEntry(params: Protocol.Storage.SetSharedStorageEntryRequest): Promise<void>;

        /**
         * Deletes entry for `key` (if it exists) for a given origin's shared storage.
         */
        deleteSharedStorageEntry(params: Protocol.Storage.DeleteSharedStorageEntryRequest): Promise<void>;

        /**
         * Clears all entries for a given origin's shared storage.
         */
        clearSharedStorageEntries(params: Protocol.Storage.ClearSharedStorageEntriesRequest): Promise<void>;

        /**
         * Resets the budget for `ownerOrigin` by clearing all budget withdrawals.
         */
        resetSharedStorageBudget(params: Protocol.Storage.ResetSharedStorageBudgetRequest): Promise<void>;

        /**
         * Enables/disables issuing of sharedStorageAccessed events.
         */
        setSharedStorageTracking(params: Protocol.Storage.SetSharedStorageTrackingRequest): Promise<void>;

        /**
         * Set tracking for a storage key's buckets.
         */
        setStorageBucketTracking(params: Protocol.Storage.SetStorageBucketTrackingRequest): Promise<void>;

        /**
         * Deletes the Storage Bucket with the given storage key and bucket name.
         */
        deleteStorageBucket(params: Protocol.Storage.DeleteStorageBucketRequest): Promise<void>;

        /**
         * Deletes state for sites identified as potential bounce trackers, immediately.
         */
        runBounceTrackingMitigations(): Promise<Protocol.Storage.RunBounceTrackingMitigationsResponse>;

        /**
         * https://wicg.github.io/attribution-reporting-api/
         */
        setAttributionReportingLocalTestingMode(params: Protocol.Storage.SetAttributionReportingLocalTestingModeRequest): Promise<void>;

        /**
         * Enables/disables issuing of Attribution Reporting events.
         */
        setAttributionReportingTracking(params: Protocol.Storage.SetAttributionReportingTrackingRequest): Promise<void>;

        /**
         * Sends all pending Attribution Reports immediately, regardless of their
         * scheduled report time.
         */
        sendPendingAttributionReports(): Promise<Protocol.Storage.SendPendingAttributionReportsResponse>;

        /**
         * Returns the effective Related Website Sets in use by this profile for the browser
         * session. The effective Related Website Sets will not change during a browser session.
         */
        getRelatedWebsiteSets(): Promise<Protocol.Storage.GetRelatedWebsiteSetsResponse>;

        /**
         * Returns the list of URLs from a page and its embedded resources that match
         * existing grace period URL pattern rules.
         * https://developers.google.com/privacy-sandbox/cookies/temporary-exceptions/grace-period
         */
        getAffectedUrlsForThirdPartyCookieMetadata(params: Protocol.Storage.GetAffectedUrlsForThirdPartyCookieMetadataRequest): Promise<Protocol.Storage.GetAffectedUrlsForThirdPartyCookieMetadataResponse>;

        setProtectedAudienceKAnonymity(params: Protocol.Storage.SetProtectedAudienceKAnonymityRequest): Promise<void>;

        /**
         * A cache's contents have been modified.
         */
        on(event: 'cacheStorageContentUpdated', listener: (params: Protocol.Storage.CacheStorageContentUpdatedEvent) => void): void;

        /**
         * A cache has been added/deleted.
         */
        on(event: 'cacheStorageListUpdated', listener: (params: Protocol.Storage.CacheStorageListUpdatedEvent) => void): void;

        /**
         * The origin's IndexedDB object store has been modified.
         */
        on(event: 'indexedDBContentUpdated', listener: (params: Protocol.Storage.IndexedDBContentUpdatedEvent) => void): void;

        /**
         * The origin's IndexedDB database list has been modified.
         */
        on(event: 'indexedDBListUpdated', listener: (params: Protocol.Storage.IndexedDBListUpdatedEvent) => void): void;

        /**
         * One of the interest groups was accessed. Note that these events are global
         * to all targets sharing an interest group store.
         */
        on(event: 'interestGroupAccessed', listener: (params: Protocol.Storage.InterestGroupAccessedEvent) => void): void;

        /**
         * An auction involving interest groups is taking place. These events are
         * target-specific.
         */
        on(event: 'interestGroupAuctionEventOccurred', listener: (params: Protocol.Storage.InterestGroupAuctionEventOccurredEvent) => void): void;

        /**
         * Specifies which auctions a particular network fetch may be related to, and
         * in what role. Note that it is not ordered with respect to
         * Network.requestWillBeSent (but will happen before loadingFinished
         * loadingFailed).
         */
        on(event: 'interestGroupAuctionNetworkRequestCreated', listener: (params: Protocol.Storage.InterestGroupAuctionNetworkRequestCreatedEvent) => void): void;

        /**
         * Shared storage was accessed by the associated page.
         * The following parameters are included in all events.
         */
        on(event: 'sharedStorageAccessed', listener: (params: Protocol.Storage.SharedStorageAccessedEvent) => void): void;

        /**
         * A shared storage run or selectURL operation finished its execution.
         * The following parameters are included in all events.
         */
        on(event: 'sharedStorageWorkletOperationExecutionFinished', listener: (params: Protocol.Storage.SharedStorageWorkletOperationExecutionFinishedEvent) => void): void;

        on(event: 'storageBucketCreatedOrUpdated', listener: (params: Protocol.Storage.StorageBucketCreatedOrUpdatedEvent) => void): void;

        on(event: 'storageBucketDeleted', listener: (params: Protocol.Storage.StorageBucketDeletedEvent) => void): void;

        on(event: 'attributionReportingSourceRegistered', listener: (params: Protocol.Storage.AttributionReportingSourceRegisteredEvent) => void): void;

        on(event: 'attributionReportingTriggerRegistered', listener: (params: Protocol.Storage.AttributionReportingTriggerRegisteredEvent) => void): void;

        on(event: 'attributionReportingReportSent', listener: (params: Protocol.Storage.AttributionReportingReportSentEvent) => void): void;

    }

    export interface SystemInfoApi {
        /**
         * Returns information about the system.
         */
        getInfo(): Promise<Protocol.SystemInfo.GetInfoResponse>;

        /**
         * Returns information about the feature state.
         */
        getFeatureState(params: Protocol.SystemInfo.GetFeatureStateRequest): Promise<Protocol.SystemInfo.GetFeatureStateResponse>;

        /**
         * Returns information about all running processes.
         */
        getProcessInfo(): Promise<Protocol.SystemInfo.GetProcessInfoResponse>;

    }

    export interface TargetApi {
        /**
         * Activates (focuses) the target.
         */
        activateTarget(params: Protocol.Target.ActivateTargetRequest): Promise<void>;

        /**
         * Attaches to the target with given id.
         */
        attachToTarget(params: Protocol.Target.AttachToTargetRequest): Promise<Protocol.Target.AttachToTargetResponse>;

        /**
         * Attaches to the browser target, only uses flat sessionId mode.
         */
        attachToBrowserTarget(): Promise<Protocol.Target.AttachToBrowserTargetResponse>;

        /**
         * Closes the target. If the target is a page that gets closed too.
         */
        closeTarget(params: Protocol.Target.CloseTargetRequest): Promise<Protocol.Target.CloseTargetResponse>;

        /**
         * Inject object to the target's main frame that provides a communication
         * channel with browser target.
         * 
         * Injected object will be available as `window[bindingName]`.
         * 
         * The object has the following API:
         * - `binding.send(json)` - a method to send messages over the remote debugging protocol
         * - `binding.onmessage = json => handleMessage(json)` - a callback that will be called for the protocol notifications and command responses.
         */
        exposeDevToolsProtocol(params: Protocol.Target.ExposeDevToolsProtocolRequest): Promise<void>;

        /**
         * Creates a new empty BrowserContext. Similar to an incognito profile but you can have more than
         * one.
         */
        createBrowserContext(params: Protocol.Target.CreateBrowserContextRequest): Promise<Protocol.Target.CreateBrowserContextResponse>;

        /**
         * Returns all browser contexts created with `Target.createBrowserContext` method.
         */
        getBrowserContexts(): Promise<Protocol.Target.GetBrowserContextsResponse>;

        /**
         * Creates a new page.
         */
        createTarget(params: Protocol.Target.CreateTargetRequest): Promise<Protocol.Target.CreateTargetResponse>;

        /**
         * Detaches session with given id.
         */
        detachFromTarget(params: Protocol.Target.DetachFromTargetRequest): Promise<void>;

        /**
         * Deletes a BrowserContext. All the belonging pages will be closed without calling their
         * beforeunload hooks.
         */
        disposeBrowserContext(params: Protocol.Target.DisposeBrowserContextRequest): Promise<void>;

        /**
         * Returns information about a target.
         */
        getTargetInfo(params: Protocol.Target.GetTargetInfoRequest): Promise<Protocol.Target.GetTargetInfoResponse>;

        /**
         * Retrieves a list of available targets.
         */
        getTargets(params: Protocol.Target.GetTargetsRequest): Promise<Protocol.Target.GetTargetsResponse>;

        /**
         * Sends protocol message over session with given id.
         * Consider using flat mode instead; see commands attachToTarget, setAutoAttach,
         * and crbug.com/991325.
         */
        sendMessageToTarget(params: Protocol.Target.SendMessageToTargetRequest): Promise<void>;

        /**
         * Controls whether to automatically attach to new targets which are considered
         * to be directly related to this one (for example, iframes or workers).
         * When turned on, attaches to all existing related targets as well. When turned off,
         * automatically detaches from all currently attached targets.
         * This also clears all targets added by `autoAttachRelated` from the list of targets to watch
         * for creation of related targets.
         * You might want to call this recursively for auto-attached targets to attach
         * to all available targets.
         */
        setAutoAttach(params: Protocol.Target.SetAutoAttachRequest): Promise<void>;

        /**
         * Adds the specified target to the list of targets that will be monitored for any related target
         * creation (such as child frames, child workers and new versions of service worker) and reported
         * through `attachedToTarget`. The specified target is also auto-attached.
         * This cancels the effect of any previous `setAutoAttach` and is also cancelled by subsequent
         * `setAutoAttach`. Only available at the Browser target.
         */
        autoAttachRelated(params: Protocol.Target.AutoAttachRelatedRequest): Promise<void>;

        /**
         * Controls whether to discover available targets and notify via
         * `targetCreated/targetInfoChanged/targetDestroyed` events.
         */
        setDiscoverTargets(params: Protocol.Target.SetDiscoverTargetsRequest): Promise<void>;

        /**
         * Enables target discovery for the specified locations, when `setDiscoverTargets` was set to
         * `true`.
         */
        setRemoteLocations(params: Protocol.Target.SetRemoteLocationsRequest): Promise<void>;

        /**
         * Issued when attached to target because of auto-attach or `attachToTarget` command.
         */
        on(event: 'attachedToTarget', listener: (params: Protocol.Target.AttachedToTargetEvent) => void): void;

        /**
         * Issued when detached from target for any reason (including `detachFromTarget` command). Can be
         * issued multiple times per target if multiple sessions have been attached to it.
         */
        on(event: 'detachedFromTarget', listener: (params: Protocol.Target.DetachedFromTargetEvent) => void): void;

        /**
         * Notifies about a new protocol message received from the session (as reported in
         * `attachedToTarget` event).
         */
        on(event: 'receivedMessageFromTarget', listener: (params: Protocol.Target.ReceivedMessageFromTargetEvent) => void): void;

        /**
         * Issued when a possible inspection target is created.
         */
        on(event: 'targetCreated', listener: (params: Protocol.Target.TargetCreatedEvent) => void): void;

        /**
         * Issued when a target is destroyed.
         */
        on(event: 'targetDestroyed', listener: (params: Protocol.Target.TargetDestroyedEvent) => void): void;

        /**
         * Issued when a target has crashed.
         */
        on(event: 'targetCrashed', listener: (params: Protocol.Target.TargetCrashedEvent) => void): void;

        /**
         * Issued when some information about a target has changed. This only happens between
         * `targetCreated` and `targetDestroyed`.
         */
        on(event: 'targetInfoChanged', listener: (params: Protocol.Target.TargetInfoChangedEvent) => void): void;

    }

    export interface TetheringApi {
        /**
         * Request browser port binding.
         */
        bind(params: Protocol.Tethering.BindRequest): Promise<void>;

        /**
         * Request browser port unbinding.
         */
        unbind(params: Protocol.Tethering.UnbindRequest): Promise<void>;

        /**
         * Informs that port was successfully bound and got a specified connection id.
         */
        on(event: 'accepted', listener: (params: Protocol.Tethering.AcceptedEvent) => void): void;

    }

    export interface TracingApi {
        /**
         * Stop trace events collection.
         */
        end(): Promise<void>;

        /**
         * Gets supported tracing categories.
         */
        getCategories(): Promise<Protocol.Tracing.GetCategoriesResponse>;

        /**
         * Record a clock sync marker in the trace.
         */
        recordClockSyncMarker(params: Protocol.Tracing.RecordClockSyncMarkerRequest): Promise<void>;

        /**
         * Request a global memory dump.
         */
        requestMemoryDump(params: Protocol.Tracing.RequestMemoryDumpRequest): Promise<Protocol.Tracing.RequestMemoryDumpResponse>;

        /**
         * Start trace events collection.
         */
        start(params: Protocol.Tracing.StartRequest): Promise<void>;

        on(event: 'bufferUsage', listener: (params: Protocol.Tracing.BufferUsageEvent) => void): void;

        /**
         * Contains a bucket of collected trace events. When tracing is stopped collected events will be
         * sent as a sequence of dataCollected events followed by tracingComplete event.
         */
        on(event: 'dataCollected', listener: (params: Protocol.Tracing.DataCollectedEvent) => void): void;

        /**
         * Signals that tracing is stopped and there is no trace buffers pending flush, all data were
         * delivered via dataCollected events.
         */
        on(event: 'tracingComplete', listener: (params: Protocol.Tracing.TracingCompleteEvent) => void): void;

    }

    export interface FetchApi {
        /**
         * Disables the fetch domain.
         */
        disable(): Promise<void>;

        /**
         * Enables issuing of requestPaused events. A request will be paused until client
         * calls one of failRequest, fulfillRequest or continueRequest/continueWithAuth.
         */
        enable(params: Protocol.Fetch.EnableRequest): Promise<void>;

        /**
         * Causes the request to fail with specified reason.
         */
        failRequest(params: Protocol.Fetch.FailRequestRequest): Promise<void>;

        /**
         * Provides response to the request.
         */
        fulfillRequest(params: Protocol.Fetch.FulfillRequestRequest): Promise<void>;

        /**
         * Continues the request, optionally modifying some of its parameters.
         */
        continueRequest(params: Protocol.Fetch.ContinueRequestRequest): Promise<void>;

        /**
         * Continues a request supplying authChallengeResponse following authRequired event.
         */
        continueWithAuth(params: Protocol.Fetch.ContinueWithAuthRequest): Promise<void>;

        /**
         * Continues loading of the paused response, optionally modifying the
         * response headers. If either responseCode or headers are modified, all of them
         * must be present.
         */
        continueResponse(params: Protocol.Fetch.ContinueResponseRequest): Promise<void>;

        /**
         * Causes the body of the response to be received from the server and
         * returned as a single string. May only be issued for a request that
         * is paused in the Response stage and is mutually exclusive with
         * takeResponseBodyForInterceptionAsStream. Calling other methods that
         * affect the request or disabling fetch domain before body is received
         * results in an undefined behavior.
         * Note that the response body is not available for redirects. Requests
         * paused in the _redirect received_ state may be differentiated by
         * `responseCode` and presence of `location` response header, see
         * comments to `requestPaused` for details.
         */
        getResponseBody(params: Protocol.Fetch.GetResponseBodyRequest): Promise<Protocol.Fetch.GetResponseBodyResponse>;

        /**
         * Returns a handle to the stream representing the response body.
         * The request must be paused in the HeadersReceived stage.
         * Note that after this command the request can't be continued
         * as is -- client either needs to cancel it or to provide the
         * response body.
         * The stream only supports sequential read, IO.read will fail if the position
         * is specified.
         * This method is mutually exclusive with getResponseBody.
         * Calling other methods that affect the request or disabling fetch
         * domain before body is received results in an undefined behavior.
         */
        takeResponseBodyAsStream(params: Protocol.Fetch.TakeResponseBodyAsStreamRequest): Promise<Protocol.Fetch.TakeResponseBodyAsStreamResponse>;

        /**
         * Issued when the domain is enabled and the request URL matches the
         * specified filter. The request is paused until the client responds
         * with one of continueRequest, failRequest or fulfillRequest.
         * The stage of the request can be determined by presence of responseErrorReason
         * and responseStatusCode -- the request is at the response stage if either
         * of these fields is present and in the request stage otherwise.
         * Redirect responses and subsequent requests are reported similarly to regular
         * responses and requests. Redirect responses may be distinguished by the value
         * of `responseStatusCode` (which is one of 301, 302, 303, 307, 308) along with
         * presence of the `location` header. Requests resulting from a redirect will
         * have `redirectedRequestId` field set.
         */
        on(event: 'requestPaused', listener: (params: Protocol.Fetch.RequestPausedEvent) => void): void;

        /**
         * Issued when the domain is enabled with handleAuthRequests set to true.
         * The request is paused until client responds with continueWithAuth.
         */
        on(event: 'authRequired', listener: (params: Protocol.Fetch.AuthRequiredEvent) => void): void;

    }

    export interface WebAudioApi {
        /**
         * Enables the WebAudio domain and starts sending context lifetime events.
         */
        enable(): Promise<void>;

        /**
         * Disables the WebAudio domain.
         */
        disable(): Promise<void>;

        /**
         * Fetch the realtime data from the registered contexts.
         */
        getRealtimeData(params: Protocol.WebAudio.GetRealtimeDataRequest): Promise<Protocol.WebAudio.GetRealtimeDataResponse>;

        /**
         * Notifies that a new BaseAudioContext has been created.
         */
        on(event: 'contextCreated', listener: (params: Protocol.WebAudio.ContextCreatedEvent) => void): void;

        /**
         * Notifies that an existing BaseAudioContext will be destroyed.
         */
        on(event: 'contextWillBeDestroyed', listener: (params: Protocol.WebAudio.ContextWillBeDestroyedEvent) => void): void;

        /**
         * Notifies that existing BaseAudioContext has changed some properties (id stays the same)..
         */
        on(event: 'contextChanged', listener: (params: Protocol.WebAudio.ContextChangedEvent) => void): void;

        /**
         * Notifies that the construction of an AudioListener has finished.
         */
        on(event: 'audioListenerCreated', listener: (params: Protocol.WebAudio.AudioListenerCreatedEvent) => void): void;

        /**
         * Notifies that a new AudioListener has been created.
         */
        on(event: 'audioListenerWillBeDestroyed', listener: (params: Protocol.WebAudio.AudioListenerWillBeDestroyedEvent) => void): void;

        /**
         * Notifies that a new AudioNode has been created.
         */
        on(event: 'audioNodeCreated', listener: (params: Protocol.WebAudio.AudioNodeCreatedEvent) => void): void;

        /**
         * Notifies that an existing AudioNode has been destroyed.
         */
        on(event: 'audioNodeWillBeDestroyed', listener: (params: Protocol.WebAudio.AudioNodeWillBeDestroyedEvent) => void): void;

        /**
         * Notifies that a new AudioParam has been created.
         */
        on(event: 'audioParamCreated', listener: (params: Protocol.WebAudio.AudioParamCreatedEvent) => void): void;

        /**
         * Notifies that an existing AudioParam has been destroyed.
         */
        on(event: 'audioParamWillBeDestroyed', listener: (params: Protocol.WebAudio.AudioParamWillBeDestroyedEvent) => void): void;

        /**
         * Notifies that two AudioNodes are connected.
         */
        on(event: 'nodesConnected', listener: (params: Protocol.WebAudio.NodesConnectedEvent) => void): void;

        /**
         * Notifies that AudioNodes are disconnected. The destination can be null, and it means all the outgoing connections from the source are disconnected.
         */
        on(event: 'nodesDisconnected', listener: (params: Protocol.WebAudio.NodesDisconnectedEvent) => void): void;

        /**
         * Notifies that an AudioNode is connected to an AudioParam.
         */
        on(event: 'nodeParamConnected', listener: (params: Protocol.WebAudio.NodeParamConnectedEvent) => void): void;

        /**
         * Notifies that an AudioNode is disconnected to an AudioParam.
         */
        on(event: 'nodeParamDisconnected', listener: (params: Protocol.WebAudio.NodeParamDisconnectedEvent) => void): void;

    }

    export interface WebAuthnApi {
        /**
         * Enable the WebAuthn domain and start intercepting credential storage and
         * retrieval with a virtual authenticator.
         */
        enable(params: Protocol.WebAuthn.EnableRequest): Promise<void>;

        /**
         * Disable the WebAuthn domain.
         */
        disable(): Promise<void>;

        /**
         * Creates and adds a virtual authenticator.
         */
        addVirtualAuthenticator(params: Protocol.WebAuthn.AddVirtualAuthenticatorRequest): Promise<Protocol.WebAuthn.AddVirtualAuthenticatorResponse>;

        /**
         * Resets parameters isBogusSignature, isBadUV, isBadUP to false if they are not present.
         */
        setResponseOverrideBits(params: Protocol.WebAuthn.SetResponseOverrideBitsRequest): Promise<void>;

        /**
         * Removes the given authenticator.
         */
        removeVirtualAuthenticator(params: Protocol.WebAuthn.RemoveVirtualAuthenticatorRequest): Promise<void>;

        /**
         * Adds the credential to the specified authenticator.
         */
        addCredential(params: Protocol.WebAuthn.AddCredentialRequest): Promise<void>;

        /**
         * Returns a single credential stored in the given virtual authenticator that
         * matches the credential ID.
         */
        getCredential(params: Protocol.WebAuthn.GetCredentialRequest): Promise<Protocol.WebAuthn.GetCredentialResponse>;

        /**
         * Returns all the credentials stored in the given virtual authenticator.
         */
        getCredentials(params: Protocol.WebAuthn.GetCredentialsRequest): Promise<Protocol.WebAuthn.GetCredentialsResponse>;

        /**
         * Removes a credential from the authenticator.
         */
        removeCredential(params: Protocol.WebAuthn.RemoveCredentialRequest): Promise<void>;

        /**
         * Clears all the credentials from the specified device.
         */
        clearCredentials(params: Protocol.WebAuthn.ClearCredentialsRequest): Promise<void>;

        /**
         * Sets whether User Verification succeeds or fails for an authenticator.
         * The default is true.
         */
        setUserVerified(params: Protocol.WebAuthn.SetUserVerifiedRequest): Promise<void>;

        /**
         * Sets whether tests of user presence will succeed immediately (if true) or fail to resolve (if false) for an authenticator.
         * The default is true.
         */
        setAutomaticPresenceSimulation(params: Protocol.WebAuthn.SetAutomaticPresenceSimulationRequest): Promise<void>;

        /**
         * Allows setting credential properties.
         * https://w3c.github.io/webauthn/#sctn-automation-set-credential-properties
         */
        setCredentialProperties(params: Protocol.WebAuthn.SetCredentialPropertiesRequest): Promise<void>;

        /**
         * Triggered when a credential is added to an authenticator.
         */
        on(event: 'credentialAdded', listener: (params: Protocol.WebAuthn.CredentialAddedEvent) => void): void;

        /**
         * Triggered when a credential is deleted, e.g. through
         * PublicKeyCredential.signalUnknownCredential().
         */
        on(event: 'credentialDeleted', listener: (params: Protocol.WebAuthn.CredentialDeletedEvent) => void): void;

        /**
         * Triggered when a credential is updated, e.g. through
         * PublicKeyCredential.signalCurrentUserDetails().
         */
        on(event: 'credentialUpdated', listener: (params: Protocol.WebAuthn.CredentialUpdatedEvent) => void): void;

        /**
         * Triggered when a credential is used in a webauthn assertion.
         */
        on(event: 'credentialAsserted', listener: (params: Protocol.WebAuthn.CredentialAssertedEvent) => void): void;

    }

    export interface MediaApi {
        /**
         * Enables the Media domain
         */
        enable(): Promise<void>;

        /**
         * Disables the Media domain.
         */
        disable(): Promise<void>;

        /**
         * This can be called multiple times, and can be used to set / override /
         * remove player properties. A null propValue indicates removal.
         */
        on(event: 'playerPropertiesChanged', listener: (params: Protocol.Media.PlayerPropertiesChangedEvent) => void): void;

        /**
         * Send events as a list, allowing them to be batched on the browser for less
         * congestion. If batched, events must ALWAYS be in chronological order.
         */
        on(event: 'playerEventsAdded', listener: (params: Protocol.Media.PlayerEventsAddedEvent) => void): void;

        /**
         * Send a list of any messages that need to be delivered.
         */
        on(event: 'playerMessagesLogged', listener: (params: Protocol.Media.PlayerMessagesLoggedEvent) => void): void;

        /**
         * Send a list of any errors that need to be delivered.
         */
        on(event: 'playerErrorsRaised', listener: (params: Protocol.Media.PlayerErrorsRaisedEvent) => void): void;

        /**
         * Called whenever a player is created, or when a new agent joins and receives
         * a list of active players. If an agent is restored, it will receive the full
         * list of player ids and all events again.
         */
        on(event: 'playersCreated', listener: (params: Protocol.Media.PlayersCreatedEvent) => void): void;

    }

    export interface DeviceAccessApi {
        /**
         * Enable events in this domain.
         */
        enable(): Promise<void>;

        /**
         * Disable events in this domain.
         */
        disable(): Promise<void>;

        /**
         * Select a device in response to a DeviceAccess.deviceRequestPrompted event.
         */
        selectPrompt(params: Protocol.DeviceAccess.SelectPromptRequest): Promise<void>;

        /**
         * Cancel a prompt in response to a DeviceAccess.deviceRequestPrompted event.
         */
        cancelPrompt(params: Protocol.DeviceAccess.CancelPromptRequest): Promise<void>;

        /**
         * A device request opened a user prompt to select a device. Respond with the
         * selectPrompt or cancelPrompt command.
         */
        on(event: 'deviceRequestPrompted', listener: (params: Protocol.DeviceAccess.DeviceRequestPromptedEvent) => void): void;

    }

    export interface PreloadApi {
        enable(): Promise<void>;

        disable(): Promise<void>;

        /**
         * Upsert. Currently, it is only emitted when a rule set added.
         */
        on(event: 'ruleSetUpdated', listener: (params: Protocol.Preload.RuleSetUpdatedEvent) => void): void;

        on(event: 'ruleSetRemoved', listener: (params: Protocol.Preload.RuleSetRemovedEvent) => void): void;

        /**
         * Fired when a preload enabled state is updated.
         */
        on(event: 'preloadEnabledStateUpdated', listener: (params: Protocol.Preload.PreloadEnabledStateUpdatedEvent) => void): void;

        /**
         * Fired when a prefetch attempt is updated.
         */
        on(event: 'prefetchStatusUpdated', listener: (params: Protocol.Preload.PrefetchStatusUpdatedEvent) => void): void;

        /**
         * Fired when a prerender attempt is updated.
         */
        on(event: 'prerenderStatusUpdated', listener: (params: Protocol.Preload.PrerenderStatusUpdatedEvent) => void): void;

        /**
         * Send a list of sources for all preloading attempts in a document.
         */
        on(event: 'preloadingAttemptSourcesUpdated', listener: (params: Protocol.Preload.PreloadingAttemptSourcesUpdatedEvent) => void): void;

    }

    export interface FedCmApi {
        enable(params: Protocol.FedCm.EnableRequest): Promise<void>;

        disable(): Promise<void>;

        selectAccount(params: Protocol.FedCm.SelectAccountRequest): Promise<void>;

        clickDialogButton(params: Protocol.FedCm.ClickDialogButtonRequest): Promise<void>;

        openUrl(params: Protocol.FedCm.OpenUrlRequest): Promise<void>;

        dismissDialog(params: Protocol.FedCm.DismissDialogRequest): Promise<void>;

        /**
         * Resets the cooldown time, if any, to allow the next FedCM call to show
         * a dialog even if one was recently dismissed by the user.
         */
        resetCooldown(): Promise<void>;

        on(event: 'dialogShown', listener: (params: Protocol.FedCm.DialogShownEvent) => void): void;

        /**
         * Triggered when a dialog is closed, either by user action, JS abort,
         * or a command below.
         */
        on(event: 'dialogClosed', listener: (params: Protocol.FedCm.DialogClosedEvent) => void): void;

    }

    export interface PWAApi {
        /**
         * Returns the following OS state for the given manifest id.
         */
        getOsAppState(params: Protocol.PWA.GetOsAppStateRequest): Promise<Protocol.PWA.GetOsAppStateResponse>;

        /**
         * Installs the given manifest identity, optionally using the given install_url
         * or IWA bundle location.
         * 
         * TODO(crbug.com/337872319) Support IWA to meet the following specific
         * requirement.
         * IWA-specific install description: If the manifest_id is isolated-app://,
         * install_url_or_bundle_url is required, and can be either an http(s) URL or
         * file:// URL pointing to a signed web bundle (.swbn). The .swbn file's
         * signing key must correspond to manifest_id. If Chrome is not in IWA dev
         * mode, the installation will fail, regardless of the state of the allowlist.
         */
        install(params: Protocol.PWA.InstallRequest): Promise<void>;

        /**
         * Uninstalls the given manifest_id and closes any opened app windows.
         */
        uninstall(params: Protocol.PWA.UninstallRequest): Promise<void>;

        /**
         * Launches the installed web app, or an url in the same web app instead of the
         * default start url if it is provided. Returns a page Target.TargetID which
         * can be used to attach to via Target.attachToTarget or similar APIs.
         */
        launch(params: Protocol.PWA.LaunchRequest): Promise<Protocol.PWA.LaunchResponse>;

        /**
         * Opens one or more local files from an installed web app identified by its
         * manifestId. The web app needs to have file handlers registered to process
         * the files. The API returns one or more page Target.TargetIDs which can be
         * used to attach to via Target.attachToTarget or similar APIs.
         * If some files in the parameters cannot be handled by the web app, they will
         * be ignored. If none of the files can be handled, this API returns an error.
         * If no files are provided as the parameter, this API also returns an error.
         * 
         * According to the definition of the file handlers in the manifest file, one
         * Target.TargetID may represent a page handling one or more files. The order
         * of the returned Target.TargetIDs is not guaranteed.
         * 
         * TODO(crbug.com/339454034): Check the existences of the input files.
         */
        launchFilesInApp(params: Protocol.PWA.LaunchFilesInAppRequest): Promise<Protocol.PWA.LaunchFilesInAppResponse>;

        /**
         * Opens the current page in its web app identified by the manifest id, needs
         * to be called on a page target. This function returns immediately without
         * waiting for the app to finish loading.
         */
        openCurrentPageInApp(params: Protocol.PWA.OpenCurrentPageInAppRequest): Promise<void>;

        /**
         * Changes user settings of the web app identified by its manifestId. If the
         * app was not installed, this command returns an error. Unset parameters will
         * be ignored; unrecognized values will cause an error.
         * 
         * Unlike the ones defined in the manifest files of the web apps, these
         * settings are provided by the browser and controlled by the users, they
         * impact the way the browser handling the web apps.
         * 
         * See the comment of each parameter.
         */
        changeAppUserSettings(params: Protocol.PWA.ChangeAppUserSettingsRequest): Promise<void>;

    }

    export interface BluetoothEmulationApi {
        /**
         * Enable the BluetoothEmulation domain.
         */
        enable(params: Protocol.BluetoothEmulation.EnableRequest): Promise<void>;

        /**
         * Set the state of the simulated central.
         */
        setSimulatedCentralState(params: Protocol.BluetoothEmulation.SetSimulatedCentralStateRequest): Promise<void>;

        /**
         * Disable the BluetoothEmulation domain.
         */
        disable(): Promise<void>;

        /**
         * Simulates a peripheral with |address|, |name| and |knownServiceUuids|
         * that has already been connected to the system.
         */
        simulatePreconnectedPeripheral(params: Protocol.BluetoothEmulation.SimulatePreconnectedPeripheralRequest): Promise<void>;

        /**
         * Simulates an advertisement packet described in |entry| being received by
         * the central.
         */
        simulateAdvertisement(params: Protocol.BluetoothEmulation.SimulateAdvertisementRequest): Promise<void>;

        /**
         * Simulates the response code from the peripheral with |address| for a
         * GATT operation of |type|. The |code| value follows the HCI Error Codes from
         * Bluetooth Core Specification Vol 2 Part D 1.3 List Of Error Codes.
         */
        simulateGATTOperationResponse(params: Protocol.BluetoothEmulation.SimulateGATTOperationResponseRequest): Promise<void>;

        /**
         * Simulates the response from the characteristic with |characteristicId| for a
         * characteristic operation of |type|. The |code| value follows the Error
         * Codes from Bluetooth Core Specification Vol 3 Part F ******* Error Response.
         * The |data| is expected to exist when simulating a successful read operation
         * response.
         */
        simulateCharacteristicOperationResponse(params: Protocol.BluetoothEmulation.SimulateCharacteristicOperationResponseRequest): Promise<void>;

        /**
         * Simulates the response from the descriptor with |descriptorId| for a
         * descriptor operation of |type|. The |code| value follows the Error
         * Codes from Bluetooth Core Specification Vol 3 Part F ******* Error Response.
         * The |data| is expected to exist when simulating a successful read operation
         * response.
         */
        simulateDescriptorOperationResponse(params: Protocol.BluetoothEmulation.SimulateDescriptorOperationResponseRequest): Promise<void>;

        /**
         * Adds a service with |serviceUuid| to the peripheral with |address|.
         */
        addService(params: Protocol.BluetoothEmulation.AddServiceRequest): Promise<Protocol.BluetoothEmulation.AddServiceResponse>;

        /**
         * Removes the service respresented by |serviceId| from the simulated central.
         */
        removeService(params: Protocol.BluetoothEmulation.RemoveServiceRequest): Promise<void>;

        /**
         * Adds a characteristic with |characteristicUuid| and |properties| to the
         * service represented by |serviceId|.
         */
        addCharacteristic(params: Protocol.BluetoothEmulation.AddCharacteristicRequest): Promise<Protocol.BluetoothEmulation.AddCharacteristicResponse>;

        /**
         * Removes the characteristic respresented by |characteristicId| from the
         * simulated central.
         */
        removeCharacteristic(params: Protocol.BluetoothEmulation.RemoveCharacteristicRequest): Promise<void>;

        /**
         * Adds a descriptor with |descriptorUuid| to the characteristic respresented
         * by |characteristicId|.
         */
        addDescriptor(params: Protocol.BluetoothEmulation.AddDescriptorRequest): Promise<Protocol.BluetoothEmulation.AddDescriptorResponse>;

        /**
         * Removes the descriptor with |descriptorId| from the simulated central.
         */
        removeDescriptor(params: Protocol.BluetoothEmulation.RemoveDescriptorRequest): Promise<void>;

        /**
         * Simulates a GATT disconnection from the peripheral with |address|.
         */
        simulateGATTDisconnection(params: Protocol.BluetoothEmulation.SimulateGATTDisconnectionRequest): Promise<void>;

        /**
         * Event for when a GATT operation of |type| to the peripheral with |address|
         * happened.
         */
        on(event: 'gattOperationReceived', listener: (params: Protocol.BluetoothEmulation.GattOperationReceivedEvent) => void): void;

        /**
         * Event for when a characteristic operation of |type| to the characteristic
         * respresented by |characteristicId| happened. |data| and |writeType| is
         * expected to exist when |type| is write.
         */
        on(event: 'characteristicOperationReceived', listener: (params: Protocol.BluetoothEmulation.CharacteristicOperationReceivedEvent) => void): void;

        /**
         * Event for when a descriptor operation of |type| to the descriptor
         * respresented by |descriptorId| happened. |data| is expected to exist when
         * |type| is write.
         */
        on(event: 'descriptorOperationReceived', listener: (params: Protocol.BluetoothEmulation.DescriptorOperationReceivedEvent) => void): void;

    }
}

export default ProtocolProxyApi;
